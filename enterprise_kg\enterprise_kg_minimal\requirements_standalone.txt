# Enterprise KG Minimal - Clean Modular Package Requirements
# Install with: pip install -r requirements_standalone.txt

# Core dependencies
neo4j>=5.0.0
python-dotenv>=1.0.0

# LLM providers (install at least one)
openai>=1.0.0          # For OpenAI GPT models
anthropic>=0.7.0       # For Anthropic Claude models

# Optional LLM providers
# google-generativeai>=0.3.0  # For Google Gemini

# Optional document processing (if you need to read files)
# pypdf>=3.0.0         # For PDF support
# python-docx>=0.8.11  # For DOCX support
