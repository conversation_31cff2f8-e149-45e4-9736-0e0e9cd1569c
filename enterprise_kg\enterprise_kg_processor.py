"""
Enterprise KG Processor

This module provides the main processor for integrating Enterprise KG
with your existing Pinecone setup. It handles document processing and
knowledge graph extraction independently.
"""

import os
import sys
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from standalone_processor import create_standalone_processor, StandaloneDocumentProcessor
from storage.neo4j_client import Neo4jClient, Neo4jConnection
from constants.schemas import ProcessingMetadata
from constants.entities import get_all_entity_types, get_person_related_types, get_project_related_types
from constants.relationships import get_all_relationship_types, get_project_relationships, get_organizational_relationships
from prompt_generator import PromptGenerator, create_full_prompt_generator

logger = logging.getLogger(__name__)


class EnterpriseKGProcessor:
    """
    Main processor for Enterprise Knowledge Graph integration.

    This class handles document processing for knowledge graph extraction
    while working alongside your existing Pinecone setup.
    """

    def __init__(
        self,
        neo4j_config: Dict[str, str],
        llm_config: Dict[str, str],
        focus_relationships: Optional[List[str]] = None,
        focus_entities: Optional[List[str]] = None,
        use_all_constants: bool = True
    ):
        """
        Initialize the Enterprise KG processor.

        Args:
            neo4j_config: Neo4j connection configuration
                {
                    "uri": "bolt://your-aura-instance.databases.neo4j.io:7687",
                    "user": "neo4j",
                    "password": "your-password",
                    "database": "neo4j"  # optional
                }
            llm_config: LLM configuration
                {
                    "provider": "openai",  # or "anthropic"
                    "model": "gpt-4o",
                    "api_key": "your-api-key"  # optional, will use env var
                }
            focus_relationships: List of relationship types to focus on (optional)
            focus_entities: List of entity types to focus on (optional)
            use_all_constants: If True, automatically use all types from constants files
        """
        self.neo4j_config = neo4j_config
        self.llm_config = llm_config
        self.use_all_constants = use_all_constants

        # Set focus relationships from constants or provided list
        if use_all_constants:
            self.focus_relationships = list(get_all_relationship_types())
            self.focus_entities = list(get_all_entity_types())
        else:
            self.focus_relationships = focus_relationships or [
                "involved_in", "mentions", "works_for", "manages", "reports_to"
            ]
            self.focus_entities = focus_entities or [
                "Person", "Project", "Company", "Department", "System"
            ]

        # Initialize the standalone processor
        self.processor = self._create_processor()

        logger.info("Enterprise KG Processor initialized")
        logger.info(f"Neo4j URI: {neo4j_config['uri']}")
        logger.info(f"LLM Provider: {llm_config['provider']}")
        logger.info(f"Use All Constants: {use_all_constants}")
        logger.info(f"Focus Entities ({len(self.focus_entities)}): {self.focus_entities[:5]}{'...' if len(self.focus_entities) > 5 else ''}")
        logger.info(f"Focus Relationships ({len(self.focus_relationships)}): {self.focus_relationships[:5]}{'...' if len(self.focus_relationships) > 5 else ''}")

    def _create_processor(self) -> StandaloneDocumentProcessor:
        """Create the standalone document processor."""
        processor = create_standalone_processor(
            neo4j_uri=self.neo4j_config["uri"],
            neo4j_user=self.neo4j_config["user"],
            neo4j_password=self.neo4j_config["password"],
            llm_provider=self.llm_config["provider"],
            llm_model=self.llm_config["model"],
            llm_api_key=self.llm_config.get("api_key"),
            use_all_constants=self.use_all_constants,
            focus_relationships=self.focus_relationships if not self.use_all_constants else None,
            focus_entities=self.focus_entities if not self.use_all_constants else None
        )

        return processor

    def test_connections(self) -> Dict[str, bool]:
        """
        Test connections to Neo4j and LLM API.

        Returns:
            Dictionary with connection test results
        """
        results = {
            "neo4j": False,
            "llm_api": False
        }

        # Test Neo4j connection
        try:
            driver = self.processor.neo4j_client._get_driver()
            with driver.session() as session:
                result = session.run("RETURN 1 as test")
                if result.single()["test"] == 1:
                    results["neo4j"] = True
                    logger.info("✓ Neo4j connection successful")
        except Exception as e:
            logger.error(f"✗ Neo4j connection failed: {e}")

        # Test LLM API
        try:
            test_response = self.processor.llm_client.generate_structured_response(
                "Test prompt", '{"test": "response"}'
            )
            if test_response:
                results["llm_api"] = True
                logger.info("✓ LLM API connection successful")
        except Exception as e:
            logger.error(f"✗ LLM API connection failed: {e}")

        return results

    def process_documents(
        self,
        document_paths: List[str],
        file_patterns: Optional[List[str]] = None
    ) -> List[ProcessingMetadata]:
        """
        Process documents for knowledge graph extraction.

        Args:
            document_paths: List of document file paths or directories
            file_patterns: File patterns to process (default: ['.md', '.txt'])

        Returns:
            List of processing metadata for each document
        """
        file_patterns = file_patterns or ['.md', '.txt']
        all_results = []

        logger.info(f"Processing {len(document_paths)} document paths")

        for path in document_paths:
            if os.path.isfile(path):
                # Single file
                logger.info(f"Processing file: {path}")
                result = self.processor.process_document(path)
                all_results.append(result)

            elif os.path.isdir(path):
                # Directory
                logger.info(f"Processing directory: {path}")
                results = self.processor.process_directory(path, file_patterns)
                all_results.extend(results)

            else:
                logger.warning(f"Path not found: {path}")

        # Log summary
        successful = sum(1 for r in all_results if r.is_completed)
        failed = len(all_results) - successful

        logger.info(f"Processing complete: {successful} successful, {failed} failed")

        return all_results

    def query_relationships(
        self,
        entity_name: Optional[str] = None,
        relationship_type: Optional[str] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Query relationships from the knowledge graph.

        Args:
            entity_name: Optional entity name to filter by
            relationship_type: Optional relationship type to filter by
            limit: Maximum number of results

        Returns:
            List of relationship dictionaries
        """
        return self.processor.neo4j_client.query_relationships(
            source_entity=entity_name,
            relationship_type=relationship_type,
            limit=limit
        )

    def get_entity_neighbors(
        self,
        entity_name: str,
        relationship_types: Optional[List[str]] = None,
        direction: str = "both",
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """
        Get neighboring entities for a given entity.

        Args:
            entity_name: Name of the central entity
            relationship_types: Optional list of relationship types to filter
            direction: Direction of relationships ("in", "out", "both")
            limit: Maximum number of results

        Returns:
            List of neighboring entities with relationship information
        """
        return self.processor.neo4j_client.get_entity_neighbors(
            entity_name=entity_name,
            relationship_types=relationship_types,
            direction=direction,
            limit=limit
        )

    def get_processing_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the knowledge graph.

        Returns:
            Dictionary with graph statistics
        """
        try:
            with self.processor.neo4j_client._get_driver().session() as session:
                # Count entities
                entity_result = session.run("MATCH (n:Entity) RETURN count(n) as count")
                entity_count = entity_result.single()["count"]

                # Count relationships
                rel_result = session.run("MATCH ()-[r]->() RETURN count(r) as count")
                relationship_count = rel_result.single()["count"]

                # Get relationship types
                type_result = session.run("""
                    MATCH ()-[r]->()
                    RETURN type(r) as rel_type, count(r) as count
                    ORDER BY count DESC
                """)
                relationship_types = {record["rel_type"]: record["count"] for record in type_result}

                return {
                    "entity_count": entity_count,
                    "relationship_count": relationship_count,
                    "relationship_types": relationship_types,
                    "timestamp": datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"Failed to get processing stats: {e}")
            return {"error": str(e)}

    def close(self):
        """Close connections and clean up resources."""
        if hasattr(self.processor, 'neo4j_client'):
            self.processor.neo4j_client.close()
        logger.info("Enterprise KG Processor closed")


def create_kg_processor(
    neo4j_uri: str = "bolt://localhost:7687",
    neo4j_user: str = "neo4j",
    neo4j_password: str = "password",
    llm_provider: str = "openai",
    llm_model: str = "gpt-4o",
    use_all_constants: bool = True
) -> EnterpriseKGProcessor:
    """
    Factory function to create an Enterprise KG processor with default settings.

    Args:
        neo4j_uri: Neo4j connection URI
        neo4j_user: Neo4j username
        neo4j_password: Neo4j password
        llm_provider: LLM provider (openai, anthropic)
        llm_model: LLM model name
        use_all_constants: Whether to use all entity and relationship constants (default: True)

    Returns:
        Configured EnterpriseKGProcessor instance
    """
    neo4j_config = {
        "uri": neo4j_uri,
        "user": neo4j_user,
        "password": neo4j_password
    }

    llm_config = {
        "provider": llm_provider,
        "model": llm_model
    }

    return EnterpriseKGProcessor(
        neo4j_config,
        llm_config,
        use_all_constants=use_all_constants
    )


# Example usage
if __name__ == "__main__":
    # Example configuration
    neo4j_config = {
        "uri": os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        "user": os.getenv("NEO4J_USER", "neo4j"),
        "password": os.getenv("NEO4J_PASSWORD", "password")
    }

    llm_config = {
        "provider": "openai",
        "model": "gpt-4o"
    }

    # Create processor
    processor = EnterpriseKGProcessor(neo4j_config, llm_config)

    # Test connections
    connections = processor.test_connections()
    print(f"Connection tests: {connections}")

    # Process sample documents (if they exist)
    if os.path.exists("documents"):
        results = processor.process_documents(["documents"])
        print(f"Processed {len(results)} documents")

        # Get stats
        stats = processor.get_processing_stats()
        print(f"Graph stats: {stats}")

    # Clean up
    processor.close()
