# Enterprise KG Integration Guide

This guide shows you how to integrate the Enterprise Knowledge Graph system with your existing Pinecone setup using the recommended **Hybrid Approach**.

## 🎯 Architecture Overview

```
Your Existing System:
📄 Documents → 🔄 Processing → 🔍 Pinecone (file_id, org_id, chunk_text)

Add Enterprise KG:
📄 Same Documents → 🤖 LLM Extraction → 🗄️ Neo4j (entities + relationships)

Combined Search:
🔍 Pinecone (semantic) + 🗄️ Neo4j (structured) = 💡 Comprehensive Results
```

## 📋 Prerequisites

- Existing Pinecone integration with metadata: `file_id`, `org_id`, `chunk_text`
- Neo4j Aura cloud instance (or local Neo4j)
- OpenAI API key (or other LLM provider)
- Python 3.9+

## 🚀 Step-by-Step Integration

### Step 1: Install Dependencies

```bash
# Navigate to enterprise_kg directory
cd enterprise_kg

# Install standalone requirements
pip install -r requirements_standalone.txt

# Additional dependencies for integration
pip install pinecone-client  # If not already installed
```

### Step 2: Environment Setup

```bash
# LLM API Key
export OPENAI_API_KEY="your-openai-api-key"

# Neo4j Aura Configuration
export NEO4J_URI="bolt://your-aura-instance.databases.neo4j.io:7687"
export NEO4J_USER="neo4j"
export NEO4J_PASSWORD="your-aura-password"

# Optional: Pinecone (if you want to test without existing setup)
export PINECONE_API_KEY="your-pinecone-api-key"
export PINECONE_ENVIRONMENT="your-pinecone-environment"
```

### Step 3: Test Standalone System

```bash
# Test the standalone system first
python test_standalone.py

# Create sample documents and test processing
python main.py --create-samples --verbose

# Test with your Neo4j Aura instance
python main.py --create-samples \
  --neo4j-uri bolt://your-aura-instance.databases.neo4j.io:7687 \
  --neo4j-user neo4j \
  --neo4j-password your-password \
  --verbose
```

### Step 4: Create Integration Components

The integration files are provided in the next sections. Create these files in your main project directory.

## 📁 Integration Files

### File 1: `enterprise_kg_processor.py`
### File 2: `hybrid_search_engine.py` 
### File 3: `integration_example.py`
### File 4: `test_integration.py`

## 🔧 Usage Examples

### Basic Integration
```python
from enterprise_kg_processor import EnterpriseKGProcessor
from hybrid_search_engine import HybridSearchEngine

# Initialize
kg_processor = EnterpriseKGProcessor(
    neo4j_config={
        "uri": "bolt://your-aura-instance.databases.neo4j.io:7687",
        "user": "neo4j",
        "password": "your-password"
    },
    llm_config={
        "provider": "openai",
        "model": "gpt-4o"
    }
)

# Process documents (same ones in your Pinecone)
document_paths = ["/path/to/your/documents"]
kg_results = kg_processor.process_documents(document_paths)

# Create hybrid search
hybrid_engine = HybridSearchEngine(
    existing_pinecone_client=your_pinecone_client,
    kg_processor=kg_processor
)

# Search with both systems
results = hybrid_engine.search("Who is working on Project Alpha?", org_id="tech_corp_001")
```

### Advanced Query Examples
```python
# Entity-focused queries
results = hybrid_engine.search("What projects is Mike Chen involved in?", org_id="tech_corp_001")

# System integration queries  
results = hybrid_engine.search("How is the CRM System connected to other systems?", org_id="tech_corp_001")

# Organizational queries
results = hybrid_engine.search("Show me the Engineering Department structure", org_id="tech_corp_001")
```

## 📊 Expected Results

### Query: "Who is working on Project Alpha?"

#### Pinecone Results (Your Existing System)
```json
{
  "semantic_search": {
    "chunks": [
      "Sarah Johnson is managing Project Alpha, which involves developing a new CRM System..."
    ],
    "source_files": ["project_alpha_report.md"]
  }
}
```

#### Neo4j Results (New Knowledge Graph)
```json
{
  "knowledge_graph": {
    "relationships": [
      {"person": "Sarah Johnson", "relationship": "MANAGES", "project": "Project Alpha"},
      {"person": "Mike Chen", "relationship": "INVOLVED_IN", "project": "Project Alpha"}
    ],
    "generated_cypher": "MATCH (person:Entity)-[r:INVOLVED_IN|MANAGES]->(project:Entity)..."
  }
}
```

#### Combined Result
```json
{
  "answer": "Based on the data, Project Alpha has Sarah Johnson as the manager and Mike Chen involved as a team member. The project involves developing a new CRM System.",
  "confidence": 0.92,
  "sources": ["project_alpha_report.md"],
  "semantic_context": "...",
  "structured_facts": "..."
}
```

## 🔍 Neo4j Queries

After processing, you can query your knowledge graph:

```cypher
// View all relationships
MATCH (n)-[r]->(m) 
RETURN n.name, type(r), m.name 
LIMIT 20

// Find people involved in projects
MATCH (person:Entity)-[:INVOLVED_IN]->(project:Entity) 
RETURN person.name, project.name

// Find management relationships
MATCH (person:Entity)-[:MANAGES]->(team:Entity) 
RETURN person.name as Manager, team.name as Team

// Find system integrations
MATCH (sys1:Entity)-[:INTEGRATES_WITH]->(sys2:Entity)
WHERE sys1.type = 'System' AND sys2.type = 'System'
RETURN sys1.name, sys2.name
```

## 🚨 Troubleshooting

### Common Issues

1. **Neo4j Connection Failed**
   ```bash
   # Test connection
   python -c "
   from enterprise_kg.storage.neo4j_client import Neo4jClient, Neo4jConnection
   conn = Neo4jConnection(uri='bolt://your-uri:7687', user='neo4j', password='your-password')
   client = Neo4jClient(conn)
   print('Connection successful!')
   "
   ```

2. **LLM API Issues**
   ```bash
   # Test API key
   python -c "
   import openai
   client = openai.OpenAI()
   response = client.chat.completions.create(
       model='gpt-4o',
       messages=[{'role': 'user', 'content': 'Hello'}]
   )
   print('API key working!')
   "
   ```

3. **Entity Extraction Issues**
   - Check document content quality
   - Verify LLM model availability
   - Review extraction prompts in `standalone_processor.py`

### Performance Optimization

1. **Batch Processing**
   ```python
   # Process documents in batches
   batch_size = 10
   for i in range(0, len(document_paths), batch_size):
       batch = document_paths[i:i + batch_size]
       kg_processor.process_documents(batch)
   ```

2. **Caching**
   ```python
   # Enable entity type caching
   kg_processor.processor.discovery_service.entity_type_cache = {}
   ```

3. **Parallel Processing**
   ```python
   # Use threading for independent operations
   import threading
   
   def process_pinecone():
       return pinecone_search(query, org_id)
   
   def process_neo4j():
       return kg_search(query, org_id)
   
   # Run in parallel
   pinecone_thread = threading.Thread(target=process_pinecone)
   neo4j_thread = threading.Thread(target=process_neo4j)
   ```

## 📈 Monitoring and Maintenance

### Health Checks
```python
def health_check():
    """Check system health."""
    checks = {
        "neo4j": test_neo4j_connection(),
        "llm_api": test_llm_api(),
        "pinecone": test_pinecone_connection()
    }
    return all(checks.values())
```

### Metrics to Track
- Document processing time
- Entity extraction accuracy
- Query response time
- Neo4j relationship count
- Pinecone chunk count

## 🔄 Migration Strategy

### Phase 1: Parallel Processing (Week 1-2)
- Set up Enterprise KG system
- Process existing documents
- Test knowledge graph quality

### Phase 2: Hybrid Integration (Week 3-4)
- Integrate hybrid search
- A/B test with existing search
- Optimize query performance

### Phase 3: Production Deployment (Week 5-6)
- Deploy to production
- Monitor performance
- Gather user feedback

### Phase 4: Optimization (Ongoing)
- Refine entity extraction
- Add new relationship types
- Scale based on usage

## 📞 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review logs with `--verbose` flag
3. Test individual components separately
4. Verify all environment variables are set

## 🎉 Success Criteria

You'll know the integration is successful when:
- ✅ Documents process without errors
- ✅ Neo4j contains extracted relationships
- ✅ Hybrid search returns both semantic and structured results
- ✅ Query response time is acceptable
- ✅ Results are more comprehensive than Pinecone alone

Ready to proceed? Start with Step 1 and work through each step systematically!
