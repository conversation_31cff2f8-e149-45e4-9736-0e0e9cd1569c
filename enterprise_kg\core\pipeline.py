"""
Processing pipeline for Enterprise KG

This module orchestrates the complete document processing pipeline:
1. Document summarization
2. Entity and relationship extraction
3. Vector storage (Pinecone)
4. Graph storage (Neo4j)
"""

import cocoindex
from typing import Optional, List, Dict, Any
from datetime import datetime
from ..constants.schemas import DocumentSummary, EntityRelationship, ProcessingMetadata
from ..storage.pinecone_client import PineconeClient, PineconeConnection
from ..storage.neo4j_client import Neo4jClient, Neo4jConnection
from .summarizer import DocumentSummarizer
from .extractor import EntityRelationshipExtractor


class ProcessingPipeline:
    """
    Complete processing pipeline for enterprise documents.
    
    This class orchestrates the entire process from document input
    to knowledge graph storage, following the CocoIndex pattern.
    """
    
    def __init__(
        self,
        summarizer: DocumentSummarizer,
        extractor: EntityRelationshipExtractor,
        pinecone_client: Optional[PineconeClient] = None,
        neo4j_client: Optional[Neo4jClient] = None,
        enable_chunking: bool = True,
        chunk_size: int = 2000,
        chunk_overlap: int = 500
    ):
        """
        Initialize the processing pipeline.
        
        Args:
            summarizer: Document summarizer instance
            extractor: Entity-relationship extractor instance
            pinecone_client: Optional Pinecone client for vector storage
            neo4j_client: Optional Neo4j client for graph storage
            enable_chunking: Whether to enable document chunking
            chunk_size: Size of document chunks
            chunk_overlap: Overlap between chunks
        """
        self.summarizer = summarizer
        self.extractor = extractor
        self.pinecone_client = pinecone_client
        self.neo4j_client = neo4j_client
        self.enable_chunking = enable_chunking
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
    
    def create_cocoindex_flow(
        self,
        flow_name: str,
        source_path: str,
        included_patterns: Optional[List[str]] = None,
        excluded_patterns: Optional[List[str]] = None
    ) -> cocoindex.FlowBuilder:
        """
        Create a CocoIndex flow for the enterprise KG pipeline.
        
        Args:
            flow_name: Name of the flow
            source_path: Path to source documents
            included_patterns: File patterns to include
            excluded_patterns: File patterns to exclude
            
        Returns:
            Configured CocoIndex flow
        """
        included_patterns = included_patterns or ["*.md", "*.txt", "*.pdf", "*.docx"]
        excluded_patterns = excluded_patterns or ["**/.*", "**/__pycache__"]
        
        @cocoindex.flow_def(name=flow_name)
        def enterprise_kg_flow(
            flow_builder: cocoindex.FlowBuilder, 
            data_scope: cocoindex.DataScope
        ):
            """Enterprise KG processing flow."""
            
            # Configure data source
            data_scope["documents"] = flow_builder.add_source(
                cocoindex.sources.LocalFile(
                    path=source_path,
                    included_patterns=included_patterns,
                    excluded_patterns=excluded_patterns
                )
            )
            
            # Collectors for different data types
            document_summaries = data_scope.add_collector()
            entity_relationships = data_scope.add_collector()
            document_chunks = data_scope.add_collector()
            
            with data_scope["documents"].row() as doc:
                # Stage 1: Document Summarization
                doc["summary"] = doc["content"].transform(
                    self.summarizer.create_extraction_function()
                )
                
                # Collect document summaries
                document_summaries.collect(
                    filename=doc["filename"],
                    title=doc["summary"]["title"],
                    summary=doc["summary"]["summary"],
                    document_type=doc["summary"]["document_type"],
                    key_topics=doc["summary"]["key_topics"]
                )
                
                # Stage 2: Entity and Relationship Extraction
                doc["relationships"] = doc["content"].transform(
                    self.extractor.create_extraction_function()
                )
                
                # Collect entity relationships
                with doc["relationships"].row() as relationship:
                    entity_relationships.collect(
                        id=cocoindex.GeneratedField.UUID,
                        subject=relationship["subject"],
                        predicate=relationship["predicate"],
                        object=relationship["object"],
                        confidence_score=relationship["confidence_score"],
                        context=relationship["context"],
                        source_sentence=relationship["source_sentence"],
                        source_document=doc["filename"]
                    )
                
                # Stage 3: Document Chunking (if enabled)
                if self.enable_chunking:
                    doc["chunks"] = doc["content"].transform(
                        cocoindex.functions.SplitRecursively(),
                        chunk_size=self.chunk_size,
                        chunk_overlap=self.chunk_overlap,
                        language="markdown"
                    )
                    
                    # Process each chunk
                    with doc["chunks"].row() as chunk:
                        # Create embeddings for chunks
                        chunk["embedding"] = chunk["text"].transform(
                            cocoindex.functions.SentenceTransformerEmbed(
                                model="sentence-transformers/all-MiniLM-L6-v2"
                            )
                        )
                        
                        # Collect chunk data
                        document_chunks.collect(
                            chunk_id=cocoindex.GeneratedField.UUID,
                            filename=doc["filename"],
                            text=chunk["text"],
                            location=chunk["location"],
                            embedding=chunk["embedding"],
                            document_title=doc["summary"]["title"],
                            document_type=doc["summary"]["document_type"]
                        )
            
            # Export to storage systems
            self._configure_exports(
                flow_builder,
                document_summaries,
                entity_relationships,
                document_chunks
            )
        
        return enterprise_kg_flow
    
    def _configure_exports(
        self,
        flow_builder: cocoindex.FlowBuilder,
        document_summaries,
        entity_relationships,
        document_chunks
    ):
        """Configure export targets for the flow."""
        
        # Export document summaries to Postgres (default CocoIndex storage)
        document_summaries.export(
            "document_summaries",
            cocoindex.storages.Postgres(table_name="document_summaries"),
            primary_key_fields=["filename"]
        )
        
        # Export entity relationships
        if self.neo4j_client:
            # Configure Neo4j connection for CocoIndex
            neo4j_conn_spec = cocoindex.add_auth_entry(
                "EnterpriseKG_Neo4j",
                cocoindex.storages.Neo4jConnection(
                    uri=self.neo4j_client.connection.uri,
                    user=self.neo4j_client.connection.user,
                    password=self.neo4j_client.connection.password,
                    db=self.neo4j_client.connection.database
                )
            )
            
            # Declare Entity nodes
            flow_builder.declare(
                cocoindex.storages.Neo4jDeclaration(
                    connection=neo4j_conn_spec,
                    nodes_label="Entity",
                    primary_key_fields=["name"]
                )
            )
            
            # Export relationships to Neo4j
            entity_relationships.export(
                "entity_relationships",
                cocoindex.storages.Neo4j(
                    connection=neo4j_conn_spec,
                    mapping=cocoindex.storages.Relationships(
                        rel_type="RELATIONSHIP",
                        source=cocoindex.storages.NodeFromFields(
                            label="Entity",
                            fields=[
                                cocoindex.storages.TargetFieldMapping(
                                    source="subject", target="name"
                                )
                            ]
                        ),
                        target=cocoindex.storages.NodeFromFields(
                            label="Entity",
                            fields=[
                                cocoindex.storages.TargetFieldMapping(
                                    source="object", target="name"
                                )
                            ]
                        )
                    )
                ),
                primary_key_fields=["id"]
            )
        else:
            # Export to Postgres if no Neo4j client
            entity_relationships.export(
                "entity_relationships",
                cocoindex.storages.Postgres(table_name="entity_relationships"),
                primary_key_fields=["id"]
            )
        
        # Export document chunks with embeddings
        if self.enable_chunking:
            if self.pinecone_client:
                # Note: CocoIndex may need specific Pinecone integration
                # For now, export to Postgres with vector support
                document_chunks.export(
                    "document_chunks",
                    cocoindex.storages.Postgres(table_name="document_chunks"),
                    primary_key_fields=["chunk_id"],
                    vector_indexes=[
                        cocoindex.VectorIndexDef(
                            field_name="embedding",
                            metric=cocoindex.VectorSimilarityMetric.COSINE_SIMILARITY
                        )
                    ]
                )
            else:
                document_chunks.export(
                    "document_chunks",
                    cocoindex.storages.Postgres(table_name="document_chunks"),
                    primary_key_fields=["chunk_id"]
                )
    
    def process_documents_batch(
        self,
        document_paths: List[str]
    ) -> List[ProcessingMetadata]:
        """
        Process a batch of documents outside of CocoIndex flow.
        
        This method provides direct processing capabilities for
        integration with existing systems.
        
        Args:
            document_paths: List of document file paths
            
        Returns:
            List of processing metadata for each document
        """
        results = []
        
        for doc_path in document_paths:
            metadata = ProcessingMetadata(
                document_id=doc_path,
                document_path=doc_path,
                file_size_bytes=0,  # Would need to calculate
                processing_start_time=datetime.now()
            )
            
            try:
                # Read document content
                with open(doc_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                metadata.file_size_bytes = len(content.encode('utf-8'))
                
                # Process document (this would need LLM client integration)
                # For now, just mark as completed
                metadata.summarization_completed = True
                metadata.entity_extraction_completed = True
                metadata.relationship_extraction_completed = True
                metadata.processing_end_time = datetime.now()
                
            except Exception as e:
                metadata.errors = [str(e)]
            
            results.append(metadata)
        
        return results


def create_default_pipeline(
    api_type: cocoindex.LlmApiType = cocoindex.LlmApiType.OPENAI,
    model: str = "gpt-4o",
    pinecone_config: Optional[Dict[str, str]] = None,
    neo4j_config: Optional[Dict[str, str]] = None
) -> ProcessingPipeline:
    """
    Create a default processing pipeline with standard settings.
    
    Args:
        api_type: LLM API type
        model: LLM model name
        pinecone_config: Optional Pinecone configuration
        neo4j_config: Optional Neo4j configuration
        
    Returns:
        Configured ProcessingPipeline instance
    """
    # Create LLM spec
    llm_spec = cocoindex.LlmSpec(api_type=api_type, model=model)
    
    # Create components
    summarizer = DocumentSummarizer(llm_spec=llm_spec)
    extractor = EntityRelationshipExtractor(llm_spec=llm_spec)
    
    # Create storage clients if configurations provided
    pinecone_client = None
    if pinecone_config:
        pinecone_conn = PineconeConnection(**pinecone_config)
        pinecone_client = PineconeClient(pinecone_conn)
    
    neo4j_client = None
    if neo4j_config:
        neo4j_conn = Neo4jConnection(**neo4j_config)
        neo4j_client = Neo4jClient(neo4j_conn)
    
    return ProcessingPipeline(
        summarizer=summarizer,
        extractor=extractor,
        pinecone_client=pinecone_client,
        neo4j_client=neo4j_client
    )
