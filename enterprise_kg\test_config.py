#!/usr/bin/env python3
"""
Test Configuration Script

This script tests the .env configuration and OpenRouter integration
without processing any documents.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_environment_variables():
    """Test that all required environment variables are loaded."""
    print("🔧 Testing Environment Variables")
    print("=" * 40)
    
    # Required variables
    required_vars = {
        "OPENROUTER_API_KEY": "OpenRouter API Key",
        "LLM_PROVIDER": "LLM Provider",
        "LLM_MODEL": "LLM Model",
        "NEO4J_URI": "Neo4j URI",
        "NEO4J_USER": "Neo4j User",
        "NEO4J_PASSWORD": "Neo4j Password"
    }
    
    all_good = True
    
    for var, description in required_vars.items():
        value = os.getenv(var)
        if value:
            # Mask sensitive values
            if "KEY" in var or "PASSWORD" in var:
                display_value = f"{value[:8]}..." if len(value) > 8 else "***"
            else:
                display_value = value
            print(f"✓ {description}: {display_value}")
        else:
            print(f"✗ {description}: Not set")
            all_good = False
    
    print()
    return all_good

def test_llm_client():
    """Test LLM client initialization."""
    print("🤖 Testing LLM Client (OpenRouter)")
    print("=" * 40)
    
    try:
        from standalone_processor import LLMClient
        
        provider = os.getenv("LLM_PROVIDER", "openrouter")
        model = os.getenv("LLM_MODEL", "anthropic/claude-3.5-sonnet")
        
        print(f"Provider: {provider}")
        print(f"Model: {model}")
        
        # Initialize client
        llm_client = LLMClient(provider=provider, model=model)
        
        if llm_client.client:
            print("✓ LLM client initialized successfully")
            
            # Test a simple request (optional - uncomment to test actual API call)
            # print("Testing API call...")
            # response = llm_client.generate_structured_response(
            #     "Say hello in JSON format",
            #     '{"message": "string"}'
            # )
            # if response:
            #     print(f"✓ API call successful: {response}")
            # else:
            #     print("✗ API call failed")
            
            return True
        else:
            print("✗ LLM client initialization failed")
            return False
            
    except Exception as e:
        print(f"✗ LLM client test failed: {e}")
        return False

def test_neo4j_connection():
    """Test Neo4j connection."""
    print("🗄️  Testing Neo4j Connection")
    print("=" * 40)
    
    try:
        from storage.neo4j_client import Neo4jClient, Neo4jConnection
        
        uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
        user = os.getenv("NEO4J_USER", "neo4j")
        password = os.getenv("NEO4J_PASSWORD", "")
        
        print(f"URI: {uri}")
        print(f"User: {user}")
        print(f"Password: {'***' if password else 'Not set'}")
        
        if not password:
            print("⚠️  Neo4j password not set - connection will likely fail")
            print("   Set NEO4J_PASSWORD in .env file")
            return False
        
        # Create connection
        conn = Neo4jConnection(uri=uri, user=user, password=password)
        client = Neo4jClient(conn)
        
        # Test connection
        driver = client._get_driver()
        with driver.session() as session:
            result = session.run("RETURN 1 as test")
            test_value = result.single()["test"]
            if test_value == 1:
                print("✓ Neo4j connection successful")
                return True
        
        client.close()
        
    except Exception as e:
        print(f"✗ Neo4j connection failed: {e}")
        print("   Make sure Neo4j is running and credentials are correct")
        return False

def test_imports():
    """Test that all required modules can be imported."""
    print("📦 Testing Module Imports")
    print("=" * 40)
    
    modules_to_test = [
        ("standalone_processor", "Standalone Processor"),
        ("storage.neo4j_client", "Neo4j Client"),
        ("constants.schemas", "Schema Definitions"),
        ("constants.entities", "Entity Constants"),
        ("constants.relationships", "Relationship Constants"),
        ("prompt_generator", "Prompt Generator"),
    ]
    
    all_good = True
    
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
            print(f"✓ {description}")
        except ImportError as e:
            print(f"✗ {description}: {e}")
            all_good = False
    
    print()
    return all_good

def main():
    """Run all configuration tests."""
    print("🚀 Enterprise KG Configuration Test")
    print("=" * 50)
    print()
    
    tests = [
        ("Environment Variables", test_environment_variables),
        ("Module Imports", test_imports),
        ("LLM Client", test_llm_client),
        ("Neo4j Connection", test_neo4j_connection),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
            results[test_name] = False
        print()
    
    # Summary
    print("📊 Test Summary")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status} {test_name}")
    
    print()
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your configuration is ready.")
        print("\nYou can now run:")
        print("  python main.py --create-samples")
        print("  python main.py")
    else:
        print("⚠️  Some tests failed. Please check your configuration.")
        print("\nCommon fixes:")
        print("  1. Set NEO4J_PASSWORD in .env file")
        print("  2. Make sure Neo4j is running")
        print("  3. Check your OpenRouter API key")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
