"""
Entity Discovery Module

This module discovers entities from Pinecone metadata and uses them to construct
dynamic Neo4j queries. This approach leverages your existing Pinecone setup.
"""

import json
import re
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass


@dataclass
class DiscoveredEntity:
    """Represents an entity discovered from Pinecone metadata."""
    name: str
    entity_type: Optional[str] = None
    confidence: float = 0.0
    source_chunk_id: str = ""
    file_id: str = ""
    context: str = ""


class EntityDiscoveryService:
    """Discovers entities from Pinecone search results and maps them to Neo4j."""
    
    def __init__(self, pinecone_client, neo4j_client, llm_client):
        self.pinecone = pinecone_client
        self.neo4j = neo4j_client
        self.llm = llm_client
        self.entity_type_cache = {}
    
    def discover_entities_from_query(self, query: str, org_id: str = None) -> List[DiscoveredEntity]:
        """
        Discover entities by first searching Pinecone, then extracting entities from results.
        """
        # Step 1: Search Pinecone for relevant chunks
        pinecone_results = self._search_pinecone(query, org_id)
        
        # Step 2: Extract entities from the returned chunks
        discovered_entities = []
        
        for result in pinecone_results.get("matches", []):
            chunk_text = result["metadata"]["chunk_text"]
            file_id = result["metadata"]["file_id"]
            chunk_id = result["id"]
            
            # Extract entities from this chunk
            entities = self._extract_entities_from_chunk(chunk_text)
            
            for entity_name in entities:
                discovered_entity = DiscoveredEntity(
                    name=entity_name,
                    confidence=result["score"],
                    source_chunk_id=chunk_id,
                    file_id=file_id,
                    context=chunk_text[:200] + "..." if len(chunk_text) > 200 else chunk_text
                )
                discovered_entities.append(discovered_entity)
        
        # Step 3: Get entity types from Neo4j
        self._enrich_with_neo4j_types(discovered_entities)
        
        return discovered_entities
    
    def _search_pinecone(self, query: str, org_id: str = None) -> Dict:
        """Search Pinecone for relevant chunks."""
        # Create query embedding
        query_embedding = self._create_embedding(query)
        
        # Build filter
        filter_metadata = {}
        if org_id:
            filter_metadata["org_id"] = org_id
        
        # Search Pinecone
        results = self.pinecone.query(
            vector=query_embedding,
            top_k=10,
            filter=filter_metadata,
            include_metadata=True
        )
        
        return results
    
    def _extract_entities_from_chunk(self, chunk_text: str) -> List[str]:
        """Extract entity names from chunk text using LLM."""
        prompt = f"""
        Extract all entity names from this text. Focus on:
        - Person names (John Smith, Sarah Johnson)
        - Project names (Project Alpha, Digital Initiative)
        - Company/Department names (Engineering Department, TechCorp)
        - System names (CRM System, ERP Platform)
        
        Text: {chunk_text}
        
        Return only the entity names, one per line.
        """
        
        try:
            response = self.llm.generate_structured_response(
                prompt, 
                '["entity1", "entity2", "entity3"]'
            )
            
            if isinstance(response, list):
                return [entity.strip() for entity in response if entity.strip()]
            else:
                # Fallback to simple regex extraction
                return self._regex_entity_extraction(chunk_text)
                
        except Exception as e:
            print(f"LLM entity extraction failed: {e}")
            return self._regex_entity_extraction(chunk_text)
    
    def _regex_entity_extraction(self, text: str) -> List[str]:
        """Fallback regex-based entity extraction."""
        # Extract capitalized phrases (potential entities)
        patterns = [
            r'\b[A-Z][a-z]+ [A-Z][a-z]+\b',  # Person names (John Smith)
            r'\b[A-Z][a-zA-Z]+ [A-Z][a-zA-Z]+(?:\s+[A-Z][a-zA-Z]+)*\b',  # Multi-word entities
            r'\b[A-Z]{2,}\s+[A-Z][a-z]+\b',  # Acronym + word (CRM System)
        ]
        
        entities = set()
        for pattern in patterns:
            matches = re.findall(pattern, text)
            entities.update(matches)
        
        # Filter out common words
        common_words = {
            "The Company", "This Project", "Our Team", "The System", 
            "New Project", "Current System", "Main Goal"
        }
        
        return [entity for entity in entities if entity not in common_words]
    
    def _enrich_with_neo4j_types(self, discovered_entities: List[DiscoveredEntity]):
        """Enrich discovered entities with types from Neo4j."""
        for entity in discovered_entities:
            if entity.name in self.entity_type_cache:
                entity.entity_type = self.entity_type_cache[entity.name]
                continue
            
            # Query Neo4j for entity type
            entity_type = self._get_entity_type_from_neo4j(entity.name)
            entity.entity_type = entity_type
            self.entity_type_cache[entity.name] = entity_type
    
    def _get_entity_type_from_neo4j(self, entity_name: str) -> Optional[str]:
        """Get entity type from Neo4j."""
        query = """
        MATCH (e:Entity)
        WHERE e.name = $entity_name OR e.name CONTAINS $entity_name
        RETURN e.type as entity_type, e.name as exact_name
        ORDER BY 
            CASE WHEN e.name = $entity_name THEN 0 ELSE 1 END,
            length(e.name)
        LIMIT 1
        """
        
        try:
            with self.neo4j._get_driver().session() as session:
                result = session.run(query, entity_name=entity_name)
                record = result.single()
                return record["entity_type"] if record else None
        except Exception as e:
            print(f"Neo4j entity type lookup failed: {e}")
            return None
    
    def _create_embedding(self, text: str) -> List[float]:
        """Create embedding for text (placeholder - use your existing method)."""
        # This should use your existing embedding creation method
        # For now, return a dummy embedding
        return [0.1] * 1536  # Placeholder
    
    def build_contextual_neo4j_query(self, discovered_entities: List[DiscoveredEntity], query_intent: str) -> Tuple[str, Dict]:
        """Build Neo4j query based on discovered entities and their types."""
        if not discovered_entities:
            return "MATCH (n:Entity) RETURN n LIMIT 10", {}
        
        # Group entities by type
        entities_by_type = {}
        for entity in discovered_entities:
            entity_type = entity.entity_type or "Unknown"
            if entity_type not in entities_by_type:
                entities_by_type[entity_type] = []
            entities_by_type[entity_type].append(entity.name)
        
        # Build query based on entity types found
        return self._construct_query_from_entity_types(entities_by_type, query_intent)
    
    def _construct_query_from_entity_types(self, entities_by_type: Dict[str, List[str]], query_intent: str) -> Tuple[str, Dict]:
        """Construct Cypher query based on entity types."""
        entity_types = list(entities_by_type.keys())
        
        # Person-centric queries
        if "Person" in entity_types or "Employee" in entity_types:
            if "Project" in entity_types:
                query = """
                MATCH (person:Entity)-[r:INVOLVED_IN|MANAGES|RESPONSIBLE_FOR]->(project:Entity)
                WHERE person.name IN $person_names 
                AND project.name IN $project_names
                AND person.type IN ['Person', 'Employee', 'Manager']
                AND project.type IN ['Project', 'Initiative', 'Program']
                RETURN person.name, person.type, type(r), project.name, project.type
                """
                
                return query, {
                    "person_names": entities_by_type.get("Person", []) + entities_by_type.get("Employee", []),
                    "project_names": entities_by_type.get("Project", [])
                }
            
            elif "System" in entity_types:
                query = """
                MATCH (person:Entity)-[r1:INVOLVED_IN|WORKS_FOR]->(intermediate:Entity)
                MATCH (intermediate)-[r2:MENTIONS|USES|INTEGRATES_WITH]->(system:Entity)
                WHERE person.name IN $person_names 
                AND system.name IN $system_names
                RETURN person.name, intermediate.name, system.name, type(r1), type(r2)
                
                UNION
                
                MATCH (person:Entity)-[r:USES|ACCESSES]->(system:Entity)
                WHERE person.name IN $person_names 
                AND system.name IN $system_names
                RETURN person.name, null, system.name, type(r), null
                """
                
                return query, {
                    "person_names": entities_by_type.get("Person", []) + entities_by_type.get("Employee", []),
                    "system_names": entities_by_type.get("System", [])
                }
        
        # Project-centric queries
        elif "Project" in entity_types:
            query = """
            MATCH (project:Entity)-[r]-(connected:Entity)
            WHERE project.name IN $project_names
            AND project.type IN ['Project', 'Initiative', 'Program']
            RETURN project.name, project.type, type(r), connected.name, connected.type
            """
            
            return query, {"project_names": entities_by_type.get("Project", [])}
        
        # System-centric queries
        elif "System" in entity_types:
            query = """
            MATCH (system:Entity)-[r]-(connected:Entity)
            WHERE system.name IN $system_names
            AND system.type IN ['System', 'Application', 'Platform']
            RETURN system.name, system.type, type(r), connected.name, connected.type
            """
            
            return query, {"system_names": entities_by_type.get("System", [])}
        
        # Generic query for unknown entity types
        else:
            all_entity_names = []
            for names in entities_by_type.values():
                all_entity_names.extend(names)
            
            query = """
            MATCH (entity:Entity)-[r]-(connected:Entity)
            WHERE entity.name IN $entity_names
            RETURN entity.name, entity.type, type(r), connected.name, connected.type
            LIMIT 20
            """
            
            return query, {"entity_names": all_entity_names}


class HybridEntitySearch:
    """Complete search system using entity discovery approach."""
    
    def __init__(self, pinecone_client, neo4j_client, llm_client):
        self.discovery_service = EntityDiscoveryService(pinecone_client, neo4j_client, llm_client)
    
    def search(self, query: str, org_id: str = None) -> Dict:
        """Perform hybrid search using entity discovery."""
        
        # Step 1: Discover entities from Pinecone results
        discovered_entities = self.discovery_service.discover_entities_from_query(query, org_id)
        
        # Step 2: Build contextual Neo4j query
        cypher_query, parameters = self.discovery_service.build_contextual_neo4j_query(
            discovered_entities, query
        )
        
        # Step 3: Execute Neo4j query
        structured_results = self._execute_neo4j_query(cypher_query, parameters)
        
        # Step 4: Get original Pinecone results for context
        pinecone_results = self.discovery_service._search_pinecone(query, org_id)
        
        return {
            "query": query,
            "discovered_entities": [
                {
                    "name": e.name,
                    "type": e.entity_type,
                    "confidence": e.confidence,
                    "file_id": e.file_id
                } for e in discovered_entities
            ],
            "semantic_context": [
                result["metadata"]["chunk_text"] 
                for result in pinecone_results.get("matches", [])
            ],
            "structured_relationships": structured_results,
            "generated_cypher": cypher_query,
            "source_files": list(set([
                result["metadata"]["file_id"] 
                for result in pinecone_results.get("matches", [])
            ]))
        }
    
    def _execute_neo4j_query(self, cypher_query: str, parameters: Dict) -> List[Dict]:
        """Execute Neo4j query and return results."""
        try:
            with self.discovery_service.neo4j._get_driver().session() as session:
                results = session.run(cypher_query, parameters)
                return [dict(record) for record in results]
        except Exception as e:
            print(f"Neo4j query execution failed: {e}")
            return []


# Usage Example
def create_entity_discovery_search(pinecone_client, neo4j_client, llm_client):
    """Factory function to create the hybrid search system."""
    return HybridEntitySearch(pinecone_client, neo4j_client, llm_client)


# Example usage:
"""
search_engine = create_entity_discovery_search(pinecone_client, neo4j_client, llm_client)

result = search_engine.search("Who is working on Project Alpha?", org_id="tech_corp_001")

# Result will include:
# - Entities discovered from Pinecone chunks
# - Their types from Neo4j
# - Dynamically constructed Cypher query
# - Structured relationship results
# - Original semantic context from Pinecone
"""
