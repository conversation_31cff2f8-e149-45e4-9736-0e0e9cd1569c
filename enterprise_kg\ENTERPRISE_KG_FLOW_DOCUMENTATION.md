# Enterprise Knowledge Graph (Enterprise KG) - Complete Flow Documentation

## Overview

The Enterprise Knowledge Graph system is a comprehensive data management solution that extracts entities and relationships from enterprise documents and builds knowledge graphs. It operates in two modes: **Standalone** (independent operation) and **Integrated** (with existing Pinecone systems), providing hybrid search capabilities that combine semantic search with structured knowledge graph queries.

## System Architecture

### Core Components
- **Document Processing Pipeline**: Orchestrates the complete flow from document input to knowledge storage
- **Entity & Relationship Extraction**: LLM-powered extraction of structured data from documents
- **Dual Storage System**: Pinecone for semantic search + Neo4j for knowledge graphs
- **Hybrid Search Engine**: Combines semantic and structured search capabilities
- **Dynamic Query Builder**: Constructs Neo4j queries based on user intent and discovered entities

### Operating Modes
1. **Standalone Mode**: Independent operation without CocoIndex dependencies
2. **Integrated Mode**: Works alongside existing Pinecone setups (two sub-modes available)
   - **Direct Integration**: Uses your existing Pinecone client directly (no CocoIndex required)
   - **CocoIndex Integration**: Uses CocoIndex framework for advanced processing workflows

---

## Integration with Existing Pinecone Systems

### Understanding the Integration Options

**Important Clarification**: When we mention "CocoIndex patterns" in integrated mode, there are two distinct approaches:

#### Option 1: Direct Integration (Recommended for existing Pinecone users)
- **No CocoIndex dependency required**
- Uses your existing Pinecone client and infrastructure directly
- Enterprise KG adds Neo4j knowledge graph capabilities alongside your current setup
- Hybrid search combines your existing Pinecone semantic search with new Neo4j structured queries
- Files involved: `integration_example.py`, `hybrid_search_engine.py`, `enterprise_kg_processor.py`

#### Option 2: CocoIndex Framework Integration
- **Requires CocoIndex framework installation**
- Uses CocoIndex's advanced processing workflows and patterns
- Provides more sophisticated document processing pipelines
- Files involved: `flows/enterprise_flow.py`, `core/pipeline.py` (CocoIndex flow definitions)

### For Your Existing Pinecone Setup

Since you already have Pinecone integrated in your main project with metadata fields (`file_id`, `org_id`, `chunk_text`), you should use **Option 1: Direct Integration**.

**What this means:**
- Keep your existing Pinecone setup unchanged
- Add Enterprise KG as a parallel system that processes the same documents
- Enterprise KG extracts entities/relationships and stores them in Neo4j
- Hybrid search queries both your existing Pinecone index AND the new Neo4j graph
- No CocoIndex framework installation needed

**Architecture:**
```
Your Current System:
Documents → Your Processing → Pinecone (file_id, org_id, chunk_text)

Add Enterprise KG:
Same Documents → Enterprise KG Processor → Neo4j (entities + relationships)

Combined Search:
Query → Pinecone (semantic) + Neo4j (structured) → Enhanced Results
```

---

## End-to-End Flow

### Stage 1: System Initialization & Configuration
**Purpose**: Set up the processing environment and validate connections

#### Files Involved:
- `main.py` - Main entry point and CLI interface
- `enterprise_kg_processor.py` - Main processor initialization
- `standalone_processor.py` - Standalone mode processor
- `storage/neo4j_client.py` - Neo4j database connection
- `storage/pinecone_client.py` - Pinecone vector database connection
- `constants/schemas.py` - Data structure definitions

#### Process Flow:
1. **Environment Setup**
   - Load configuration from environment variables or CLI arguments
   - Initialize LLM client (OpenAI, Anthropic, etc.)
   - Establish Neo4j connection (local or Neo4j Aura cloud)
   - Optional: Connect to existing Pinecone index

2. **Connection Validation**
   - Test Neo4j connectivity and authentication
   - Verify LLM API access and model availability
   - Validate Pinecone index access (if integrated mode)

3. **Processor Configuration**
   - Configure entity and relationship types to extract
   - Set processing parameters (chunking, summarization options)
   - Initialize prompt generators for LLM interactions

---

### Stage 2: Document Discovery & Preparation
**Purpose**: Identify and prepare documents for processing

#### Files Involved:
- `main.py` - Document discovery logic
- `utils/helpers.py` - File processing utilities
- Sample document creation utilities

#### Process Flow:
1. **Document Discovery**
   - Scan specified directories for documents
   - Filter by file patterns (*.md, *.txt, *.pdf, etc.)
   - Create document processing queue

2. **Sample Data Generation** (Optional)
   - Generate sample enterprise documents for testing
   - Create realistic person-project relationship scenarios
   - Populate test directory structure

3. **Document Validation**
   - Verify file accessibility and format
   - Check document encoding and readability
   - Log processing queue statistics

---

### Stage 3: Document Processing Pipeline
**Purpose**: Extract structured information from documents

#### Files Involved:
- `core/pipeline.py` - Main processing orchestration
- `core/summarizer.py` - Document summarization
- `core/extractor.py` - Entity and relationship extraction
- `constants/entities.py` - Entity type definitions
- `constants/relationships.py` - Relationship type definitions
- `prompt_generator.py` - LLM prompt generation

#### Process Flow:
1. **Document Summarization**
   - Generate document summaries focusing on key entities
   - Extract document metadata (title, type, key themes)
   - Create processing metadata tracking

2. **Entity Extraction**
   - Identify entities using predefined types (Person, Project, Organization, etc.)
   - Extract entity properties and attributes
   - Assign confidence scores to extracted entities

3. **Relationship Extraction**
   - Identify relationships between extracted entities
   - Focus on key relationship types (involved_in, manages, depends_on, etc.)
   - Create structured relationship data with properties

4. **Data Validation**
   - Validate extracted entities against schema definitions
   - Check relationship consistency and completeness
   - Generate processing completion metadata

---

### Stage 4: Knowledge Storage
**Purpose**: Store extracted knowledge in appropriate databases

#### Files Involved:
- `storage/neo4j_client.py` - Graph database operations
- `storage/pinecone_client.py` - Vector database operations
- `constants/schemas.py` - Storage schema definitions

#### Process Flow:
1. **Neo4j Graph Storage**
   - Create entity nodes with properties and labels
   - Establish relationship edges between entities
   - Apply graph constraints and indexing
   - Store document metadata and processing timestamps

2. **Pinecone Vector Storage** (Integrated Mode)
   - Generate embeddings for document chunks
   - Store vectors with metadata (file_id, org_id, chunk_text)
   - Maintain consistency with existing Pinecone data structure
   - Index for semantic search capabilities

3. **Storage Validation**
   - Verify successful data persistence
   - Check data integrity and relationships
   - Update processing status and completion metrics

---

### Stage 5: Hybrid Search & Query Processing
**Purpose**: Provide intelligent search capabilities combining semantic and structured approaches

#### Files Involved:
- `hybrid_search_engine.py` - Main search orchestration
- `knowledge_enrichment.py` - Template-based query building
- `entity_discovery.py` - Entity discovery from search results
- `prompt_generator.py` - Query enhancement prompts

#### Process Flow:
1. **Query Analysis**
   - Detect user intent from natural language queries
   - Extract potential entity names and types
   - Determine optimal search strategy

2. **Semantic Search** (Pinecone)
   - Generate query embeddings
   - Search vector database for relevant document chunks
   - Filter by organization ID and other metadata
   - Retrieve contextual information

3. **Structured Search** (Neo4j)
   - **Template-Based Approach**: Use predefined query patterns based on intent
   - **Entity Discovery Approach**: Discover entities from Pinecone results, then query Neo4j
   - Generate dynamic Cypher queries
   - Execute graph traversals and relationship queries

4. **Result Combination**
   - Merge semantic and structured search results
   - Rank and score combined results
   - Generate comprehensive answers using LLM
   - Provide source attribution and confidence scores

---

### Stage 6: Integration & API Layer
**Purpose**: Integrate with existing systems and provide API access

#### Files Involved:
- `integration_example.py` - Integration patterns
- `hybrid_search_example.py` - Complete search examples
- `example_usage.py` - Usage demonstrations
- `flows/enterprise_flow.py` - CocoIndex flow definitions

#### Process Flow:
1. **System Integration**
   - Connect with existing Pinecone infrastructure
   - Maintain compatibility with current data schemas
   - Provide seamless API integration points

2. **Enhanced Search Services**
   - Offer multiple search methods (template, discovery, hybrid, auto)
   - Provide fallback mechanisms for different query types
   - Support organization-based data filtering

3. **Result Enhancement**
   - Combine existing search results with knowledge graph insights
   - Generate enriched responses with relationship context
   - Provide explanation of query processing and results

---

## File Organization by Stage and Mode

### Stage 1 - Initialization
**Standalone Mode:**
- `main.py` - CLI and main entry point
- `standalone_processor.py` - Standalone mode implementation

**Direct Integration Mode (for existing Pinecone users):**
- `enterprise_kg_processor.py` - Main processor class
- `integration_example.py` - Integration with existing Pinecone

**CocoIndex Integration Mode:**
- `flows/enterprise_flow.py` - CocoIndex flow definitions
- `core/pipeline.py` - CocoIndex processing orchestration

**Common:**
- `storage/neo4j_client.py` - Neo4j connection management
- `storage/pinecone_client.py` - Pinecone connection management

### Stage 2 - Document Preparation
- `utils/helpers.py` - File processing utilities
- Sample document generation functions in `main.py`

### Stage 3 - Processing Pipeline
**Standalone/Direct Integration:**
- `standalone_processor.py` - Direct LLM-based extraction
- `enterprise_kg_processor.py` - Integration processor

**CocoIndex Integration:**
- `core/pipeline.py` - CocoIndex processing orchestration
- `core/summarizer.py` - Document summarization
- `core/extractor.py` - Entity/relationship extraction

**Common:**
- `prompt_generator.py` - LLM prompt generation
- `constants/entities.py` - Entity definitions
- `constants/relationships.py` - Relationship definitions
- `constants/schemas.py` - Data schemas

### Stage 4 - Storage
- `storage/neo4j_client.py` - Graph database operations
- `storage/pinecone_client.py` - Vector database operations (CocoIndex mode)

### Stage 5 - Search & Query
**Direct Integration (recommended for existing Pinecone users):**
- `hybrid_search_engine.py` - Main search engine for existing Pinecone integration
- `knowledge_enrichment.py` - Template-based queries
- `entity_discovery.py` - Entity discovery approach

### Stage 6 - Integration Examples
**Direct Integration:**
- `integration_example.py` - Direct integration patterns
- `hybrid_search_example.py` - Complete search examples

**CocoIndex Integration:**
- `flows/enterprise_flow.py` - CocoIndex flows
- `example_usage.py` - CocoIndex usage demonstrations

### Testing & Validation
- `test_standalone.py` - Standalone mode tests
- `test_integration.py` - Direct integration tests
- `quick_start.py` - Quick start examples

---

## Data Flow Summary

1. **Input**: Enterprise documents (Markdown, text, PDF)
2. **Processing**: LLM-powered extraction → Entities + Relationships
3. **Storage**: Neo4j (graph) + Pinecone (vectors)
4. **Query**: Natural language → Hybrid search (semantic + structured)
5. **Output**: Enhanced results with relationship context

## Key Features

- **Dual Storage Strategy**: Combines vector similarity with graph relationships
- **Dynamic Query Construction**: Builds Neo4j queries based on user intent
- **Entity Discovery**: Finds entities in Pinecone results, then queries Neo4j
- **Template-Based Queries**: Uses predefined patterns for common query types
- **Hybrid Search**: Automatically selects best search strategy
- **Integration Ready**: Works with existing Pinecone infrastructure
- **Scalable Architecture**: Supports enterprise-scale document processing

## Configuration Options

- **Entity Types**: Configurable entity extraction (Person, Project, Organization, etc.)
- **Relationship Types**: Configurable relationship patterns (involved_in, manages, etc.)
- **LLM Providers**: Support for OpenAI, Anthropic, and other providers
- **Database Options**: Local Neo4j or Neo4j Aura cloud instances
- **Processing Modes**: Standalone or integrated with existing systems
- **Search Strategies**: Template-based, entity discovery, or hybrid approaches

---

## Recommended Approach for Your Project

Based on your existing Pinecone setup with metadata fields (`file_id`, `org_id`, `chunk_text`), here's the recommended integration path:

### Use Direct Integration Mode (No CocoIndex Required)

**Key Files to Focus On:**
1. `enterprise_kg_processor.py` - Main processor for your integration
2. `hybrid_search_engine.py` - Combines your Pinecone with Neo4j
3. `integration_example.py` - Complete integration example
4. `standalone_processor.py` - Core document processing logic

**Integration Steps:**
1. **Keep your existing Pinecone system unchanged**
2. **Add Enterprise KG as a parallel processing system**
3. **Process the same documents through Enterprise KG → Neo4j**
4. **Use hybrid search to query both systems simultaneously**

**Benefits:**
- No additional framework dependencies
- Minimal changes to your existing codebase
- Enhanced search capabilities with relationship context
- Scalable architecture that works with your current infrastructure

**What You Get:**
- Your existing semantic search (Pinecone) continues to work
- New structured relationship queries (Neo4j) are added
- Hybrid search combines both for comprehensive results
- Dynamic query building based on user intent and discovered entities

This approach gives you the best of both worlds: keeping your proven Pinecone infrastructure while adding powerful knowledge graph capabilities through Neo4j.
