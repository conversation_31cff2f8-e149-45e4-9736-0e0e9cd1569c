#!/usr/bin/env python3
"""
Test script for standalone Enterprise KG

This script tests the standalone functionality without requiring
external dependencies like Neo4j or LLM APIs.
"""

import os
import sys
import tempfile
import shutil
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from constants.entities import EntityType, get_all_entity_types
from constants.relationships import RelationshipType, get_all_relationship_types
from constants.schemas import EntityRelationship, DocumentSummary
from utils.helpers import (
    validate_entity_relationship, 
    clean_entity_name, 
    normalize_relationship_type,
    create_sample_documents
)


def test_constants():
    """Test entity and relationship constants."""
    print("🧪 Testing constants...")
    
    # Test entity types
    entity_types = get_all_entity_types()
    assert len(entity_types) > 0, "No entity types found"
    assert EntityType.PERSON.value in entity_types, "Person entity type missing"
    assert EntityType.PROJECT.value in entity_types, "Project entity type missing"
    print(f"   ✓ Found {len(entity_types)} entity types")
    
    # Test relationship types
    rel_types = get_all_relationship_types()
    assert len(rel_types) > 0, "No relationship types found"
    assert RelationshipType.INVOLVED_IN.value in rel_types, "involved_in relationship missing"
    assert RelationshipType.MENTIONS.value in rel_types, "mentions relationship missing"
    print(f"   ✓ Found {len(rel_types)} relationship types")


def test_schemas():
    """Test schema validation."""
    print("🧪 Testing schemas...")
    
    # Test valid entity relationship
    valid_rel = EntityRelationship(
        subject="John Smith",
        predicate="involved_in", 
        object="Project Alpha",
        confidence_score=0.9
    )
    
    errors = validate_entity_relationship(valid_rel)
    assert len(errors) == 0, f"Valid relationship failed validation: {errors}"
    print("   ✓ Valid relationship passes validation")
    
    # Test invalid entity relationship
    invalid_rel = EntityRelationship(
        subject="",
        predicate="invalid_relationship",
        object="Project Alpha",
        confidence_score=1.5  # Invalid score
    )
    
    errors = validate_entity_relationship(invalid_rel)
    assert len(errors) > 0, "Invalid relationship should fail validation"
    print(f"   ✓ Invalid relationship caught {len(errors)} errors")
    
    # Test document summary
    summary = DocumentSummary(
        title="Test Document",
        summary="This is a test summary",
        document_type="report"
    )
    assert summary.title == "Test Document", "Document summary creation failed"
    print("   ✓ Document summary creation works")


def test_utilities():
    """Test utility functions."""
    print("🧪 Testing utilities...")
    
    # Test entity name cleaning
    dirty_name = "  John   Smith  "
    clean_name = clean_entity_name(dirty_name)
    assert clean_name == "John Smith", f"Name cleaning failed: '{clean_name}'"
    print("   ✓ Entity name cleaning works")
    
    # Test relationship normalization
    dirty_rel = "is involved in"
    normalized_rel = normalize_relationship_type(dirty_rel)
    assert normalized_rel == "involved_in", f"Relationship normalization failed: '{normalized_rel}'"
    print("   ✓ Relationship normalization works")


def test_sample_documents():
    """Test sample document creation."""
    print("🧪 Testing sample document creation...")
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        created_files = create_sample_documents(temp_dir)
        
        assert len(created_files) > 0, "No sample documents created"
        print(f"   ✓ Created {len(created_files)} sample documents")
        
        # Check that files exist and have content
        for file_path in created_files:
            assert os.path.exists(file_path), f"Sample file not found: {file_path}"
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                assert len(content) > 100, f"Sample file too short: {file_path}"
        
        print("   ✓ Sample documents have valid content")


def test_neo4j_client_import():
    """Test Neo4j client import (without connection)."""
    print("🧪 Testing Neo4j client import...")
    
    try:
        from storage.neo4j_client import Neo4jConnection, Neo4jClient
        
        # Test connection object creation
        conn = Neo4jConnection(
            uri="bolt://localhost:7687",
            user="neo4j",
            password="test"
        )
        assert conn.uri == "bolt://localhost:7687", "Neo4j connection creation failed"
        print("   ✓ Neo4j connection object creation works")
        
        # Test client creation (without actual connection)
        client = Neo4jClient(conn)
        assert client.connection == conn, "Neo4j client creation failed"
        print("   ✓ Neo4j client creation works")
        
    except ImportError as e:
        print(f"   ⚠️  Neo4j client import failed (expected if neo4j not installed): {e}")


def test_standalone_processor_import():
    """Test standalone processor import."""
    print("🧪 Testing standalone processor import...")
    
    try:
        from standalone_processor import LLMClient, StandaloneDocumentProcessor
        
        # Test LLM client creation (without API key)
        llm_client = LLMClient(provider="openai", model="gpt-4o", api_key="test")
        assert llm_client.provider == "openai", "LLM client creation failed"
        assert llm_client.model == "gpt-4o", "LLM client model setting failed"
        print("   ✓ LLM client creation works")
        
    except ImportError as e:
        print(f"   ⚠️  Standalone processor import failed: {e}")


def test_main_script_import():
    """Test main script import."""
    print("🧪 Testing main script import...")
    
    try:
        # Import main module functions
        import main
        
        # Test argument parsing
        args = main.parse_arguments()
        assert hasattr(args, 'documents'), "Argument parsing failed"
        print("   ✓ Main script import and argument parsing works")
        
    except ImportError as e:
        print(f"   ⚠️  Main script import failed: {e}")
    except SystemExit:
        # This is expected when running without arguments
        print("   ✓ Main script import works (argument parsing exit expected)")


def run_all_tests():
    """Run all tests."""
    print("🚀 Running Enterprise KG Standalone Tests")
    print("=" * 50)
    
    tests = [
        test_constants,
        test_schemas,
        test_utilities,
        test_sample_documents,
        test_neo4j_client_import,
        test_standalone_processor_import,
        test_main_script_import,
    ]
    
    passed = 0
    failed = 0
    
    for test_func in tests:
        try:
            test_func()
            passed += 1
        except Exception as e:
            print(f"   ✗ Test failed: {e}")
            failed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! The standalone system is ready to use.")
        print("\nNext steps:")
        print("1. Install dependencies: pip install -r requirements_standalone.txt")
        print("2. Set up Neo4j and LLM API keys")
        print("3. Run: python main.py --create-samples")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    return failed == 0


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
