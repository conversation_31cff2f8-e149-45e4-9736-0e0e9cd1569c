"""
Integration Example

This script demonstrates how to integrate the Enterprise KG system
with your existing Pinecone setup. Follow this example to integrate
with your actual system.
"""

import os
import sys
import json
import logging
from typing import Dict, List, Any

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from enterprise_kg_processor import EnterpriseKGProcessor
from hybrid_search_engine import HybridSearchEngine
from utils.helpers import setup_environment, print_setup_summary

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class YourExistingPineconeSystem:
    """
    Mock class representing your existing Pinecone system.
    Replace this with your actual Pinecone integration.
    """
    
    def __init__(self, api_key: str, environment: str, index_name: str):
        """Initialize your existing Pinecone system."""
        self.api_key = api_key
        self.environment = environment
        self.index_name = index_name
        
        # TODO: Replace with your actual Pinecone initialization
        logger.info(f"Initialized Pinecone: {environment}/{index_name}")
    
    def create_embedding(self, text: str) -> List[float]:
        """
        Create embedding for text.
        Replace this with your actual embedding method.
        """
        # TODO: Replace with your actual embedding creation
        # This could be OpenAI embeddings, SentenceTransformers, etc.
        logger.debug(f"Creating embedding for: {text[:50]}...")
        return [0.1] * 1536  # Placeholder
    
    def search(self, query: str, org_id: str = None, top_k: int = 10) -> Dict[str, Any]:
        """
        Your existing Pinecone search method.
        Replace this with your actual search implementation.
        """
        # TODO: Replace with your actual Pinecone search
        query_embedding = self.create_embedding(query)
        
        # Mock Pinecone response - replace with actual query
        mock_response = {
            "matches": [
                {
                    "id": "doc1_chunk1",
                    "score": 0.92,
                    "metadata": {
                        "file_id": "project_alpha_report.md",
                        "org_id": org_id or "default_org",
                        "chunk_text": "Sarah Johnson is managing Project Alpha, which involves developing a new CRM System. The project team includes Mike Chen as the lead developer."
                    }
                },
                {
                    "id": "doc1_chunk2", 
                    "score": 0.87,
                    "metadata": {
                        "file_id": "project_alpha_report.md",
                        "org_id": org_id or "default_org",
                        "chunk_text": "The Engineering Department, led by Jennifer Walsh, is collaborating on Project Alpha. Mike Chen reports that the CRM integration is on track."
                    }
                },
                {
                    "id": "doc2_chunk1",
                    "score": 0.83,
                    "metadata": {
                        "file_id": "team_structure.md",
                        "org_id": org_id or "default_org",
                        "chunk_text": "Mike Chen works for the Engineering Department and is involved in multiple projects including the digital transformation initiative."
                    }
                }
            ]
        }
        
        logger.info(f"Pinecone search for '{query}' returned {len(mock_response['matches'])} results")
        return mock_response
    
    def get_index_stats(self) -> Dict[str, Any]:
        """Get statistics about your Pinecone index."""
        # TODO: Replace with actual Pinecone stats
        return {
            "total_vectors": 10000,
            "dimension": 1536,
            "index_fullness": 0.1
        }


class IntegratedEnterpriseSystem:
    """
    Your integrated system combining existing Pinecone with new Enterprise KG.
    """
    
    def __init__(
        self,
        pinecone_config: Dict[str, str],
        neo4j_config: Dict[str, str],
        llm_config: Dict[str, str]
    ):
        """Initialize the integrated system."""
        logger.info("Initializing Integrated Enterprise System...")
        
        # Initialize your existing Pinecone system
        self.pinecone_system = YourExistingPineconeSystem(
            api_key=pinecone_config["api_key"],
            environment=pinecone_config["environment"],
            index_name=pinecone_config["index_name"]
        )
        
        # Initialize Enterprise KG processor
        self.kg_processor = EnterpriseKGProcessor(neo4j_config, llm_config)
        
        # Initialize hybrid search engine
        self.hybrid_search = HybridSearchEngine(
            existing_pinecone_client=self.pinecone_system,
            kg_processor=self.kg_processor
        )
        
        logger.info("✓ Integrated Enterprise System initialized")
    
    def process_new_documents(self, document_paths: List[str]) -> Dict[str, Any]:
        """
        Process new documents for both Pinecone and Knowledge Graph.
        
        Args:
            document_paths: List of document paths to process
            
        Returns:
            Processing results from both systems
        """
        logger.info(f"Processing {len(document_paths)} documents...")
        
        results = {
            "pinecone_processing": {},
            "kg_processing": {},
            "total_documents": len(document_paths)
        }
        
        # Process for your existing Pinecone system
        # TODO: Replace with your actual document processing pipeline
        logger.info("Processing documents for Pinecone...")
        results["pinecone_processing"] = {
            "status": "completed",
            "chunks_created": len(document_paths) * 5,  # Mock
            "message": "Replace with your actual Pinecone processing"
        }
        
        # Process for Knowledge Graph
        logger.info("Processing documents for Knowledge Graph...")
        kg_results = self.kg_processor.process_documents(document_paths)
        
        successful_kg = sum(1 for r in kg_results if r.is_completed)
        failed_kg = len(kg_results) - successful_kg
        
        results["kg_processing"] = {
            "status": "completed" if failed_kg == 0 else "partial",
            "successful": successful_kg,
            "failed": failed_kg,
            "details": kg_results
        }
        
        logger.info(f"✓ Document processing complete: Pinecone={results['pinecone_processing']['status']}, KG={results['kg_processing']['status']}")
        return results
    
    def enhanced_search(self, query: str, org_id: str = None) -> Dict[str, Any]:
        """
        Enhanced search using both Pinecone and Knowledge Graph.
        
        Args:
            query: Search query
            org_id: Organization ID for filtering
            
        Returns:
            Enhanced search results
        """
        logger.info(f"Enhanced search: '{query}' (org_id: {org_id})")
        
        # Perform hybrid search
        hybrid_results = self.hybrid_search.search(query, org_id, method="hybrid")
        
        # Add your existing search for comparison
        existing_results = self.pinecone_system.search(query, org_id)
        
        # Combine and enhance
        enhanced_results = {
            "query": query,
            "org_id": org_id,
            "hybrid_search": hybrid_results,
            "existing_pinecone": {
                "chunks": [match["metadata"]["chunk_text"] for match in existing_results["matches"]],
                "source_files": list(set([match["metadata"]["file_id"] for match in existing_results["matches"]])),
                "total_results": len(existing_results["matches"])
            },
            "enhancement_summary": {
                "semantic_chunks": len(hybrid_results.get("semantic_search", {}).get("chunks", [])),
                "structured_relationships": len(hybrid_results.get("knowledge_graph", {}).get("relationships", [])),
                "total_sources": len(hybrid_results.get("source_files", [])),
                "confidence": hybrid_results.get("confidence", 0.0)
            }
        }
        
        logger.info(f"✓ Enhanced search complete: {enhanced_results['enhancement_summary']}")
        return enhanced_results
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get statistics about the integrated system."""
        return {
            "pinecone_stats": self.pinecone_system.get_index_stats(),
            "kg_stats": self.kg_processor.get_processing_stats(),
            "hybrid_search_stats": self.hybrid_search.get_search_stats()
        }
    
    def test_all_connections(self) -> Dict[str, bool]:
        """Test all system connections."""
        logger.info("Testing all system connections...")
        
        # Test KG connections
        kg_connections = self.kg_processor.test_connections()
        
        # Test Pinecone (mock)
        pinecone_connected = True  # TODO: Replace with actual Pinecone test
        
        results = {
            "pinecone": pinecone_connected,
            "neo4j": kg_connections["neo4j"],
            "llm_api": kg_connections["llm_api"]
        }
        
        all_connected = all(results.values())
        logger.info(f"Connection tests: {results} (All connected: {all_connected})")
        
        return results


def main():
    """Main integration example."""
    print("🚀 Enterprise KG Integration Example")
    print("=" * 50)
    
    # Step 1: Environment setup
    print("\n1. Setting up environment...")
    setup_info = setup_environment(create_sample_docs=True, sample_docs_dir="documents")
    print_setup_summary(setup_info)
    
    # Step 2: Configuration
    print("\n2. Configuring systems...")
    
    # Pinecone configuration (replace with your actual config)
    pinecone_config = {
        "api_key": os.getenv("PINECONE_API_KEY", "mock-api-key"),
        "environment": os.getenv("PINECONE_ENVIRONMENT", "us-west1-gcp"),
        "index_name": "enterprise-kg-demo"
    }
    
    # Neo4j configuration
    neo4j_config = {
        "uri": os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        "user": os.getenv("NEO4J_USER", "neo4j"),
        "password": os.getenv("NEO4J_PASSWORD", "password")
    }
    
    # LLM configuration
    llm_config = {
        "provider": "openai",
        "model": "gpt-4o"
    }
    
    print(f"✓ Pinecone: {pinecone_config['environment']}/{pinecone_config['index_name']}")
    print(f"✓ Neo4j: {neo4j_config['uri']}")
    print(f"✓ LLM: {llm_config['provider']}/{llm_config['model']}")
    
    # Step 3: Initialize integrated system
    print("\n3. Initializing integrated system...")
    try:
        integrated_system = IntegratedEnterpriseSystem(
            pinecone_config=pinecone_config,
            neo4j_config=neo4j_config,
            llm_config=llm_config
        )
        print("✓ Integrated system initialized")
    except Exception as e:
        print(f"✗ Failed to initialize system: {e}")
        return
    
    # Step 4: Test connections
    print("\n4. Testing connections...")
    connections = integrated_system.test_all_connections()
    if not all(connections.values()):
        print("⚠️  Some connections failed. Check your configuration.")
        print("💡 You can still test with mock data.")
    
    # Step 5: Process documents
    print("\n5. Processing documents...")
    if os.path.exists("documents"):
        document_paths = ["documents"]
        processing_results = integrated_system.process_new_documents(document_paths)
        print(f"✓ Processing complete: {processing_results['kg_processing']['successful']} documents processed")
    else:
        print("⚠️  No documents directory found. Skipping document processing.")
    
    # Step 6: Test enhanced search
    print("\n6. Testing enhanced search...")
    
    test_queries = [
        "Who is working on Project Alpha?",
        "What systems does Mike Chen work with?",
        "Show me the Engineering Department structure",
        "How is the CRM System connected to other systems?"
    ]
    
    for query in test_queries:
        print(f"\n--- Query: {query} ---")
        try:
            results = integrated_system.enhanced_search(query, org_id="tech_corp_001")
            
            # Print summary
            summary = results["enhancement_summary"]
            print(f"Semantic chunks: {summary['semantic_chunks']}")
            print(f"Structured relationships: {summary['structured_relationships']}")
            print(f"Confidence: {summary['confidence']:.2f}")
            
            # Print answer if available
            answer = results["hybrid_search"].get("answer")
            if answer:
                print(f"Answer: {answer}")
            
        except Exception as e:
            print(f"Search failed: {e}")
    
    # Step 7: System statistics
    print("\n7. System statistics...")
    try:
        stats = integrated_system.get_system_stats()
        print(f"Knowledge Graph: {stats['kg_stats'].get('entity_count', 0)} entities, {stats['kg_stats'].get('relationship_count', 0)} relationships")
        print(f"Pinecone: {stats['pinecone_stats']['total_vectors']} vectors")
    except Exception as e:
        print(f"Failed to get stats: {e}")
    
    print("\n✅ Integration example complete!")
    print("\nNext steps:")
    print("1. Replace YourExistingPineconeSystem with your actual Pinecone integration")
    print("2. Update the create_embedding method with your embedding logic")
    print("3. Configure your actual Neo4j and LLM credentials")
    print("4. Test with your real documents and queries")


if __name__ == "__main__":
    main()
