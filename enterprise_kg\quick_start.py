#!/usr/bin/env python3
"""
Quick Start Guide for Enterprise KG Integration

This script provides a quick way to get started with the Enterprise KG system.
Run this script to set up and test the integration step by step.
"""

import os
import sys
import json
import logging
from typing import Dict, Any

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_environment() -> Dict[str, Any]:
    """Check environment setup and requirements."""
    print("🔍 Checking environment...")
    
    env_status = {
        "python_version": sys.version,
        "current_directory": os.getcwd(),
        "environment_variables": {},
        "required_files": {},
        "recommendations": []
    }
    
    # Check environment variables
    required_env_vars = [
        "OPENAI_API_KEY",
        "NEO4J_URI", 
        "NEO4J_USER",
        "NEO4J_PASSWORD"
    ]
    
    for var in required_env_vars:
        value = os.getenv(var)
        env_status["environment_variables"][var] = "SET" if value else "NOT_SET"
        if not value:
            env_status["recommendations"].append(f"Set {var} environment variable")
    
    # Check required files
    required_files = [
        "enterprise_kg_processor.py",
        "hybrid_search_engine.py", 
        "standalone_processor.py",
        "storage/neo4j_client.py",
        "constants/schemas.py"
    ]
    
    for file_path in required_files:
        exists = os.path.exists(file_path)
        env_status["required_files"][file_path] = "EXISTS" if exists else "MISSING"
        if not exists:
            env_status["recommendations"].append(f"Ensure {file_path} exists")
    
    # Print status
    print(f"✓ Python version: {sys.version.split()[0]}")
    print(f"✓ Current directory: {os.getcwd()}")
    
    print("\nEnvironment Variables:")
    for var, status in env_status["environment_variables"].items():
        symbol = "✓" if status == "SET" else "✗"
        print(f"  {symbol} {var}: {status}")
    
    print("\nRequired Files:")
    for file_path, status in env_status["required_files"].items():
        symbol = "✓" if status == "EXISTS" else "✗"
        print(f"  {symbol} {file_path}: {status}")
    
    if env_status["recommendations"]:
        print("\nRecommendations:")
        for i, rec in enumerate(env_status["recommendations"], 1):
            print(f"  {i}. {rec}")
    
    return env_status


def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")
    
    try:
        import subprocess
        
        # Install from requirements file if it exists
        if os.path.exists("requirements_standalone.txt"):
            print("Installing from requirements_standalone.txt...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements_standalone.txt"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✓ Dependencies installed successfully")
            else:
                print(f"✗ Failed to install dependencies: {result.stderr}")
                return False
        else:
            # Install individual packages
            packages = ["neo4j", "openai", "anthropic"]
            for package in packages:
                print(f"Installing {package}...")
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", package
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    print(f"✓ {package} installed")
                else:
                    print(f"✗ Failed to install {package}: {result.stderr}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error installing dependencies: {e}")
        return False


def create_sample_config() -> Dict[str, Any]:
    """Create sample configuration."""
    print("\n⚙️  Creating sample configuration...")
    
    config = {
        "neo4j": {
            "uri": os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            "user": os.getenv("NEO4J_USER", "neo4j"),
            "password": os.getenv("NEO4J_PASSWORD", "password"),
            "database": os.getenv("NEO4J_DATABASE", None)
        },
        "llm": {
            "provider": "openai",
            "model": "gpt-4o",
            "api_key": os.getenv("OPENAI_API_KEY")
        },
        "pinecone": {
            "api_key": os.getenv("PINECONE_API_KEY", "your-pinecone-api-key"),
            "environment": os.getenv("PINECONE_ENVIRONMENT", "us-west1-gcp"),
            "index_name": "enterprise-kg-demo"
        }
    }
    
    # Save config to file
    with open("config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print("✓ Configuration saved to config.json")
    print(f"✓ Neo4j URI: {config['neo4j']['uri']}")
    print(f"✓ LLM Provider: {config['llm']['provider']}")
    
    return config


def test_basic_functionality(config: Dict[str, Any]):
    """Test basic functionality."""
    print("\n🧪 Testing basic functionality...")
    
    try:
        # Test imports
        print("Testing imports...")
        from enterprise_kg_processor import EnterpriseKGProcessor
        from hybrid_search_engine import HybridSearchEngine
        print("✓ All imports successful")
        
        # Test KG processor creation
        print("Testing KG processor creation...")
        kg_processor = EnterpriseKGProcessor(
            neo4j_config=config["neo4j"],
            llm_config=config["llm"]
        )
        print("✓ KG processor created")
        
        # Test connections
        print("Testing connections...")
        connections = kg_processor.test_connections()
        
        for service, status in connections.items():
            symbol = "✓" if status else "✗"
            print(f"  {symbol} {service}: {'Connected' if status else 'Failed'}")
        
        # Test with mock Pinecone
        print("Testing hybrid search engine...")
        
        class MockPineconeClient:
            def query(self, vector, top_k, filter=None, include_metadata=True):
                return {"matches": []}
        
        mock_pinecone = MockPineconeClient()
        hybrid_search = HybridSearchEngine(mock_pinecone, kg_processor)
        print("✓ Hybrid search engine created")
        
        # Clean up
        kg_processor.close()
        
        return True
        
    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        return False


def create_sample_documents():
    """Create sample documents for testing."""
    print("\n📄 Creating sample documents...")
    
    try:
        from utils.helpers import create_sample_documents as create_samples
        
        if not os.path.exists("documents"):
            created_files = create_samples("documents")
            print(f"✓ Created {len(created_files)} sample documents in documents/")
            
            for file_path in created_files:
                print(f"  - {os.path.basename(file_path)}")
        else:
            print("✓ Documents directory already exists")
        
        return True
        
    except Exception as e:
        print(f"✗ Failed to create sample documents: {e}")
        return False


def run_integration_test():
    """Run a simple integration test."""
    print("\n🚀 Running integration test...")
    
    try:
        # Load config
        with open("config.json", "r") as f:
            config = json.load(f)
        
        # Test the integration example
        from integration_example import IntegratedEnterpriseSystem
        
        print("Creating integrated system...")
        integrated_system = IntegratedEnterpriseSystem(
            pinecone_config=config["pinecone"],
            neo4j_config=config["neo4j"],
            llm_config=config["llm"]
        )
        
        print("Testing enhanced search...")
        results = integrated_system.enhanced_search(
            query="Who is working on Project Alpha?",
            org_id="test_org"
        )
        
        print("✓ Integration test successful")
        print(f"  - Query: {results['query']}")
        print(f"  - Semantic chunks: {results['enhancement_summary']['semantic_chunks']}")
        print(f"  - Structured relationships: {results['enhancement_summary']['structured_relationships']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Integration test failed: {e}")
        return False


def print_next_steps():
    """Print next steps for the user."""
    print("\n🎯 Next Steps")
    print("=" * 50)
    
    print("\n1. Configure Your Actual Systems:")
    print("   - Update config.json with your real Neo4j Aura credentials")
    print("   - Set your OpenAI API key")
    print("   - Replace mock Pinecone with your actual Pinecone client")
    
    print("\n2. Process Your Documents:")
    print("   python enterprise_kg_processor.py")
    print("   # Or use the integration example")
    
    print("\n3. Test Hybrid Search:")
    print("   python integration_example.py")
    
    print("\n4. Run Full Test Suite:")
    print("   python test_integration.py")
    
    print("\n5. Integration with Your System:")
    print("   - Replace YourExistingPineconeSystem in integration_example.py")
    print("   - Update the create_embedding method")
    print("   - Integrate hybrid_search_engine.py into your application")
    
    print("\n📚 Documentation:")
    print("   - Check README_STANDALONE.md for detailed instructions")
    print("   - Review integration_example.py for implementation patterns")
    print("   - Use test_integration.py to verify your setup")


def main():
    """Main quick start function."""
    print("🚀 Enterprise KG Quick Start")
    print("=" * 50)
    print("This script will help you set up and test the Enterprise KG integration.")
    print()
    
    # Step 1: Check environment
    env_status = check_environment()
    
    # Step 2: Install dependencies
    if input("\nInstall dependencies? (y/n): ").lower().startswith('y'):
        install_dependencies()
    
    # Step 3: Create configuration
    config = create_sample_config()
    
    # Step 4: Test basic functionality
    if test_basic_functionality(config):
        print("✅ Basic functionality test passed!")
    else:
        print("❌ Basic functionality test failed. Check your configuration.")
        return
    
    # Step 5: Create sample documents
    if input("\nCreate sample documents? (y/n): ").lower().startswith('y'):
        create_sample_documents()
    
    # Step 6: Run integration test
    if input("\nRun integration test? (y/n): ").lower().startswith('y'):
        if run_integration_test():
            print("✅ Integration test passed!")
        else:
            print("❌ Integration test failed. Check your connections.")
    
    # Step 7: Print next steps
    print_next_steps()
    
    print("\n🎉 Quick start complete!")
    print("You're ready to integrate Enterprise KG with your existing system.")


if __name__ == "__main__":
    main()
