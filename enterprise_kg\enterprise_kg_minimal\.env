# =============================================================================
# Enterprise KG Configuration
# =============================================================================

# -----------------------------------------------------------------------------
# LLM Configuration (OpenRouter)
# -----------------------------------------------------------------------------
OPENROUTER_API_KEY="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"
LLM_PROVIDER="openrouter"
LLM_MODEL="anthropic/claude-3.5-sonnet"

REQUESTY_API_KEY="sk-fmSlsd0uQ6WH8r2OUrQKrh5JWsWAM4Y1N/AimMjGbs3VqbYT/tZKkxskRvpVGmxeBoxvbwLUV29DaScUQ/jL+8aR34UgsNwDxETrzaMw6g0="
REQUESTY_BASE_URL="https://router.requesty.ai/v1"

# Alternative LLM providers (uncomment to use)
# OPENAI_API_KEY=""
# ANTHROPIC_API_KEY=""
# GEMINI_API_KEY=""

# -----------------------------------------------------------------------------
# Neo4j Configuration
# -----------------------------------------------------------------------------
NEO4J_URI="neo4j+ssc://62238a67.databases.neo4j.io"
NEO4J_USER="neo4j"
NEO4J_PASSWORD="_G5SjaDLvrnC-yMv09WoETvlFXTc2zeVSS-smLsQ2Vc"
NEO4J_DATABASE="neo4j"

# For Neo4j Aura cloud instance, use format like:
# NEO4J_URI="bolt://your-instance.databases.neo4j.io:7687"
# NEO4J_USER="neo4j"
# NEO4J_PASSWORD="your-aura-password"

# -----------------------------------------------------------------------------
# Document Processing Configuration
# -----------------------------------------------------------------------------
DOCUMENTS_PATH="documents"
FILE_PATTERNS=".md,.txt,.pdf"

# Processing options
ENABLE_SUMMARIZATION="true"
USE_ALL_CONSTANTS="true"

# Focus on specific relationships (if USE_ALL_CONSTANTS=false)
FOCUS_RELATIONSHIPS="involved_in,mentions,works_for,manages,reports_to"
FOCUS_ENTITIES="Person,Project,Company,Department,System"

# -----------------------------------------------------------------------------
# Optional: Pinecone Configuration (for integration mode)
# -----------------------------------------------------------------------------
PINECONE_API_KEY=""
PINECONE_ENVIRONMENT=""
PINECONE_INDEX_NAME="enterprise-kg"
