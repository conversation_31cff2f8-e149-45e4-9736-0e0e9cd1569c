"""
Helper functions for Enterprise KG

This module contains utility functions for data validation,
environment setup, and sample data creation.
"""

import os
import re
from typing import List, Dict, Any, Optional
from constants.schemas import EntityRelationship
from constants.entities import is_valid_entity_type
from constants.relationships import is_valid_relationship_type


def validate_entity_relationship(entity_rel: EntityRelationship) -> List[str]:
    """
    Validate an entity relationship and return any validation errors.

    Args:
        entity_rel: Entity relationship to validate

    Returns:
        List of validation error messages (empty if valid)
    """
    errors = []

    # Check required fields
    if not entity_rel.subject or not entity_rel.subject.strip():
        errors.append("Subject cannot be empty")

    if not entity_rel.predicate or not entity_rel.predicate.strip():
        errors.append("Predicate cannot be empty")

    if not entity_rel.object or not entity_rel.object.strip():
        errors.append("Object cannot be empty")

    # Check relationship type validity
    if entity_rel.predicate and not is_valid_relationship_type(entity_rel.predicate):
        errors.append(f"Invalid relationship type: {entity_rel.predicate}")

    # Check confidence score range
    if entity_rel.confidence_score is not None:
        if not (0.0 <= entity_rel.confidence_score <= 1.0):
            errors.append("Confidence score must be between 0.0 and 1.0")

    return errors


def clean_entity_name(entity_name: str) -> str:
    """
    Clean and normalize an entity name.

    Args:
        entity_name: Raw entity name

    Returns:
        Cleaned entity name
    """
    if not entity_name:
        return ""

    # Remove extra whitespace
    cleaned = re.sub(r'\s+', ' ', entity_name.strip())

    # Remove special characters that might cause issues
    cleaned = re.sub(r'[^\w\s\-\.\(\)&]', '', cleaned)

    # Capitalize properly (title case for names)
    if cleaned.replace(' ', '').replace('-', '').replace('.', '').isalpha():
        cleaned = cleaned.title()

    return cleaned


def normalize_relationship_type(relationship_type: str) -> str:
    """
    Normalize a relationship type to match our constants.

    Args:
        relationship_type: Raw relationship type

    Returns:
        Normalized relationship type
    """
    if not relationship_type:
        return ""

    # Convert to lowercase and replace spaces/hyphens with underscores
    normalized = relationship_type.lower().strip()
    normalized = re.sub(r'[\s\-]+', '_', normalized)

    # Common mappings
    mappings = {
        'is_involved_in': 'involved_in',
        'participates_in': 'involved_in',
        'works_on': 'involved_in',
        'is_part_of': 'part_of',
        'belongs_to': 'part_of',
        'is_managed_by': 'reports_to',
        'reports_to': 'reports_to',
        'is_responsible_for': 'responsible_for',
        'owns': 'responsible_for',
    }

    return mappings.get(normalized, normalized)


def create_sample_documents(output_dir: str = "sample_documents") -> List[str]:
    """
    Create sample documents for testing the enterprise KG system.

    Args:
        output_dir: Directory to create sample documents in

    Returns:
        List of created document file paths
    """
    os.makedirs(output_dir, exist_ok=True)

    sample_docs = [
        {
            "filename": "project_alpha_report.md",
            "content": """# Project Alpha Status Report

## Executive Summary
Project Alpha is a strategic initiative led by Sarah Johnson, our Senior Project Manager.
The project involves developing a new customer relationship management system.

## Team Members
- Sarah Johnson (Project Manager) - responsible for overall project coordination
- Mike Chen (Lead Developer) - involved in system architecture and development
- Lisa Rodriguez (UX Designer) - responsible for user interface design
- David Kim (Data Analyst) - involved in data migration planning

## Project Details
Project Alpha is scheduled for completion in Q4 2024. The project mentions several key stakeholders
including the IT Department and the Sales Team. The CRM System will integrate with our existing
ERP Platform.

## Budget and Resources
The project has been allocated a budget of $500,000 and involves collaboration with TechCorp,
our primary technology vendor.
"""
        },
        {
            "filename": "team_structure.md",
            "content": """# Organizational Structure - Engineering Department

## Department Overview
The Engineering Department is led by Jennifer Walsh, VP of Engineering. The department
consists of three main teams.

## Team Structure

### Backend Development Team
- Team Lead: Alex Thompson
- Members: Mike Chen, Robert Davis, Amanda Wilson
- The team is responsible for API development and database management

### Frontend Development Team
- Team Lead: Lisa Rodriguez
- Members: Kevin Park, Maria Santos, James Liu
- This team works on user interfaces and client applications

### DevOps Team
- Team Lead: David Kim
- Members: Sarah Mitchell, Tom Bradley
- The DevOps team manages deployment pipelines and infrastructure

## Reporting Structure
All team leads report to Jennifer Walsh. The Engineering Department collaborates closely
with the Product Management team led by Rachel Green.

## Current Projects
The department is currently involved in Project Alpha and the Digital Transformation Initiative.
"""
        },
        {
            "filename": "vendor_contracts.md",
            "content": """# Vendor Contracts and Partnerships

## Technology Partners

### TechCorp Partnership
TechCorp is our primary technology vendor for cloud infrastructure. The contract was
negotiated by procurement manager John Stevens and approved by CFO Patricia Moore.

### DataSolutions Inc.
DataSolutions provides analytics and business intelligence tools. The partnership
was established through our CTO Michael Chang and involves integration with our
Data Warehouse system.

## Contract Details

### TechCorp Contract
- Contract Value: $2.5M annually
- Services: Cloud hosting, technical support, consulting
- Key Contact: Jennifer Walsh (our side), Mark Johnson (TechCorp)
- The contract mentions specific SLA requirements and performance metrics

### DataSolutions Contract
- Contract Value: $800K annually
- Services: BI Platform licensing, data analytics consulting
- Key Contact: Michael Chang (our side), Susan Lee (DataSolutions)

## Compliance and Governance
All vendor contracts are reviewed by the Legal Department led by Thomas Wilson.
The Procurement team, managed by John Stevens, handles contract negotiations.
"""
        }
    ]

    created_files = []

    for doc in sample_docs:
        file_path = os.path.join(output_dir, doc["filename"])
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(doc["content"])
        created_files.append(file_path)

    return created_files


def setup_environment(
    create_sample_docs: bool = True,
    sample_docs_dir: str = "documents"
) -> Dict[str, Any]:
    """
    Set up the environment for running Enterprise KG.

    Args:
        create_sample_docs: Whether to create sample documents
        sample_docs_dir: Directory for sample documents

    Returns:
        Dictionary with setup information
    """
    setup_info = {
        "sample_documents_created": False,
        "sample_documents_path": None,
        "environment_variables": {},
        "recommendations": []
    }

    # Create sample documents if requested
    if create_sample_docs:
        try:
            created_files = create_sample_documents(sample_docs_dir)
            setup_info["sample_documents_created"] = True
            setup_info["sample_documents_path"] = sample_docs_dir
            setup_info["created_files"] = created_files
        except Exception as e:
            setup_info["recommendations"].append(f"Failed to create sample documents: {e}")

    # Check environment variables
    env_vars = [
        "OPENAI_API_KEY",
        "PINECONE_API_KEY",
        "PINECONE_ENVIRONMENT",
        "NEO4J_URI",
        "NEO4J_USER",
        "NEO4J_PASSWORD"
    ]

    for var in env_vars:
        value = os.getenv(var)
        setup_info["environment_variables"][var] = "SET" if value else "NOT_SET"

        if not value:
            setup_info["recommendations"].append(f"Consider setting {var} environment variable")

    # Add setup recommendations
    if not setup_info["environment_variables"]["OPENAI_API_KEY"]:
        setup_info["recommendations"].append(
            "Set OPENAI_API_KEY for LLM-based extraction"
        )

    if not setup_info["environment_variables"]["NEO4J_URI"]:
        setup_info["recommendations"].append(
            "Set up Neo4j database and configure connection variables"
        )

    if not setup_info["environment_variables"]["PINECONE_API_KEY"]:
        setup_info["recommendations"].append(
            "Set up Pinecone account and configure API key for vector storage"
        )

    return setup_info


def print_setup_summary(setup_info: Dict[str, Any]):
    """
    Print a summary of the environment setup.

    Args:
        setup_info: Setup information from setup_environment()
    """
    print("=== Enterprise KG Environment Setup ===")
    print()

    if setup_info["sample_documents_created"]:
        print(f"✓ Sample documents created in: {setup_info['sample_documents_path']}")
        print(f"  Created {len(setup_info.get('created_files', []))} sample files")
    else:
        print("✗ Sample documents not created")

    print()
    print("Environment Variables:")
    for var, status in setup_info["environment_variables"].items():
        status_symbol = "✓" if status == "SET" else "✗"
        print(f"  {status_symbol} {var}: {status}")

    if setup_info["recommendations"]:
        print()
        print("Recommendations:")
        for i, rec in enumerate(setup_info["recommendations"], 1):
            print(f"  {i}. {rec}")

    print()
    print("=== Setup Complete ===")


def get_entity_relationship_stats(relationships: List[EntityRelationship]) -> Dict[str, Any]:
    """
    Get statistics about a list of entity relationships.

    Args:
        relationships: List of entity relationships

    Returns:
        Dictionary with statistics
    """
    if not relationships:
        return {"total": 0}

    predicates = [rel.predicate for rel in relationships]
    subjects = [rel.subject for rel in relationships]
    objects = [rel.object for rel in relationships]

    stats = {
        "total": len(relationships),
        "unique_predicates": len(set(predicates)),
        "unique_subjects": len(set(subjects)),
        "unique_objects": len(set(objects)),
        "predicate_counts": {},
        "most_common_predicates": [],
        "entities_with_most_relationships": []
    }

    # Count predicates
    for predicate in predicates:
        stats["predicate_counts"][predicate] = stats["predicate_counts"].get(predicate, 0) + 1

    # Most common predicates
    sorted_predicates = sorted(
        stats["predicate_counts"].items(),
        key=lambda x: x[1],
        reverse=True
    )
    stats["most_common_predicates"] = sorted_predicates[:5]

    # Count entity mentions
    entity_counts = {}
    for rel in relationships:
        entity_counts[rel.subject] = entity_counts.get(rel.subject, 0) + 1
        entity_counts[rel.object] = entity_counts.get(rel.object, 0) + 1

    # Most mentioned entities
    sorted_entities = sorted(
        entity_counts.items(),
        key=lambda x: x[1],
        reverse=True
    )
    stats["entities_with_most_relationships"] = sorted_entities[:10]

    return stats
