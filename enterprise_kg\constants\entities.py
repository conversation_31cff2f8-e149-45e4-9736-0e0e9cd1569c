"""
Entity type definitions for Enterprise KG

This module defines all supported entity types in the enterprise knowledge graph.
Add new entity types here as the system evolves.
"""

from enum import Enum
from typing import Set


class EntityType(Enum):
    """
    Enumeration of all supported entity types in the enterprise knowledge graph.
    
    Each entity type represents a distinct category of entities that can be
    extracted from enterprise documents.
    """
    
    # People and Roles
    PERSON = "Person"
    EMPLOYEE = "Employee"
    MANAGER = "Manager"
    EXECUTIVE = "Executive"
    CONSULTANT = "Consultant"
    CLIENT = "Client"
    STAKEHOLDER = "Stakeholder"
    
    # Projects and Initiatives
    PROJECT = "Project"
    INITIATIVE = "Initiative"
    PROGRAM = "Program"
    CAMPAIGN = "Campaign"
    
    # Organizations
    COMPANY = "Company"
    DEPARTMENT = "Department"
    TEAM = "Team"
    VENDOR = "Vendor"
    PARTNER = "Partner"
    COMPETITOR = "Competitor"
    
    # Documents and Assets
    DOCUMENT = "Document"
    REPORT = "Report"
    PROPOSAL = "Proposal"
    CONTRACT = "Contract"
    POLICY = "Policy"
    PROCEDURE = "Procedure"
    
    # Technology and Systems
    SYSTEM = "System"
    APPLICATION = "Application"
    DATABASE = "Database"
    PLATFORM = "Platform"
    TOOL = "Tool"
    TECHNOLOGY = "Technology"
    
    # Business Concepts
    PROCESS = "Process"
    WORKFLOW = "Workflow"
    REQUIREMENT = "Requirement"
    OBJECTIVE = "Objective"
    GOAL = "Goal"
    METRIC = "Metric"
    KPI = "KPI"
    
    # Financial
    BUDGET = "Budget"
    COST = "Cost"
    REVENUE = "Revenue"
    INVESTMENT = "Investment"
    
    # Locations
    OFFICE = "Office"
    LOCATION = "Location"
    REGION = "Region"
    COUNTRY = "Country"
    
    # Time-based
    MILESTONE = "Milestone"
    DEADLINE = "Deadline"
    PHASE = "Phase"
    QUARTER = "Quarter"
    YEAR = "Year"
    
    # Generic
    ENTITY = "Entity"  # Fallback for unclassified entities


# Helper functions for entity type management
def get_all_entity_types() -> Set[str]:
    """Get all entity type values as strings."""
    return {entity_type.value for entity_type in EntityType}


def get_person_related_types() -> Set[str]:
    """Get entity types related to people and roles."""
    return {
        EntityType.PERSON.value,
        EntityType.EMPLOYEE.value,
        EntityType.MANAGER.value,
        EntityType.EXECUTIVE.value,
        EntityType.CONSULTANT.value,
        EntityType.CLIENT.value,
        EntityType.STAKEHOLDER.value,
    }


def get_project_related_types() -> Set[str]:
    """Get entity types related to projects and initiatives."""
    return {
        EntityType.PROJECT.value,
        EntityType.INITIATIVE.value,
        EntityType.PROGRAM.value,
        EntityType.CAMPAIGN.value,
    }


def get_organization_related_types() -> Set[str]:
    """Get entity types related to organizations."""
    return {
        EntityType.COMPANY.value,
        EntityType.DEPARTMENT.value,
        EntityType.TEAM.value,
        EntityType.VENDOR.value,
        EntityType.PARTNER.value,
        EntityType.COMPETITOR.value,
    }


def is_valid_entity_type(entity_type: str) -> bool:
    """Check if a string is a valid entity type."""
    return entity_type in get_all_entity_types()


def get_entity_type_description(entity_type: EntityType) -> str:
    """Get a human-readable description for an entity type."""
    descriptions = {
        EntityType.PERSON: "An individual person mentioned in the document",
        EntityType.EMPLOYEE: "A company employee",
        EntityType.MANAGER: "A person in a management role",
        EntityType.EXECUTIVE: "A senior executive or C-level person",
        EntityType.CONSULTANT: "An external consultant or advisor",
        EntityType.CLIENT: "A customer or client of the organization",
        EntityType.STAKEHOLDER: "A person with interest in the project/organization",
        
        EntityType.PROJECT: "A specific project or work initiative",
        EntityType.INITIATIVE: "A strategic initiative or program",
        EntityType.PROGRAM: "A large-scale program containing multiple projects",
        EntityType.CAMPAIGN: "A marketing or business campaign",
        
        EntityType.COMPANY: "A company or corporation",
        EntityType.DEPARTMENT: "An organizational department",
        EntityType.TEAM: "A working team or group",
        EntityType.VENDOR: "An external vendor or supplier",
        EntityType.PARTNER: "A business partner",
        EntityType.COMPETITOR: "A competitor organization",
        
        EntityType.DOCUMENT: "A document or file",
        EntityType.REPORT: "A formal report",
        EntityType.PROPOSAL: "A business proposal",
        EntityType.CONTRACT: "A legal contract or agreement",
        EntityType.POLICY: "An organizational policy",
        EntityType.PROCEDURE: "A standard operating procedure",
        
        EntityType.SYSTEM: "A computer or business system",
        EntityType.APPLICATION: "A software application",
        EntityType.DATABASE: "A database system",
        EntityType.PLATFORM: "A technology platform",
        EntityType.TOOL: "A software tool or utility",
        EntityType.TECHNOLOGY: "A technology or technical solution",
        
        EntityType.PROCESS: "A business process",
        EntityType.WORKFLOW: "A defined workflow",
        EntityType.REQUIREMENT: "A business or technical requirement",
        EntityType.OBJECTIVE: "A business objective",
        EntityType.GOAL: "A specific goal or target",
        EntityType.METRIC: "A measurement or metric",
        EntityType.KPI: "A key performance indicator",
        
        EntityType.BUDGET: "A financial budget",
        EntityType.COST: "A cost or expense",
        EntityType.REVENUE: "Revenue or income",
        EntityType.INVESTMENT: "An investment or funding",
        
        EntityType.OFFICE: "A physical office location",
        EntityType.LOCATION: "A geographic location",
        EntityType.REGION: "A geographic region",
        EntityType.COUNTRY: "A country",
        
        EntityType.MILESTONE: "A project milestone",
        EntityType.DEADLINE: "A deadline or due date",
        EntityType.PHASE: "A project phase",
        EntityType.QUARTER: "A business quarter",
        EntityType.YEAR: "A specific year",
        
        EntityType.ENTITY: "A generic entity",
    }
    
    return descriptions.get(entity_type, "Unknown entity type")
