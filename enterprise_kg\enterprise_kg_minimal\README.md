# Enterprise KG Minimal - Production Ready Package

A clean, modular enterprise knowledge graph system that creates chunk-based knowledge graphs from documents. This package is production-ready and designed for easy integration into existing systems.

## 🎯 Key Features

- **📊 Chunk-Based Architecture**: File → Chunks → Entities graph structure
- **🔧 Simple API**: Single `process_document()` function interface
- **🧩 Advanced Chunking**: Multiple strategies (hybrid, sentence-based, paragraph-based)
- **🤖 LLM Integration**: Multi-provider support with entity/relationship extraction
- **🗄️ Neo4j Storage**: Scalable graph database with proper relationships
- **📦 Modular Design**: Clean separation of concerns for maintainability
- **✅ Production Ready**: Comprehensive error handling and logging

## Architecture

### Module Structure

```
enterprise_kg_minimal/
├── __init__.py                    # Main importable function
├── core/                          # Core processing modules
│   ├── __init__.py
│   ├── document_processor.py      # Main processing logic
│   ├── chunking_engine.py         # Document chunking
│   └── graph_builder.py           # Graph construction
├── llm/                          # LLM integration
│   ├── __init__.py
│   └── client.py                 # LLM client
├── storage/                      # Data storage
│   ├── __init__.py
│   └── neo4j_client.py          # Neo4j operations
├── constants/                    # Constants and schemas
│   ├── __init__.py
│   ├── entities.py
│   ├── relationships.py
│   └── schemas.py
└── utils/                        # Utilities
    ├── __init__.py
    └── helpers.py
```

### Graph Structure

The system creates a hierarchical graph structure:

1. **File Node**: Represents the source document
2. **Chunk Nodes**: Individual chunks of the document
3. **Entity Nodes**: Extracted entities from each chunk
4. **Relationships**:
   - `CONTAINS`: File → Chunk
   - `EXTRACTED_FROM`: Chunk → Entity
   - Domain relationships between entities

## 🚀 Quick Start

### Installation

```bash
# Install required dependencies
pip install neo4j python-dotenv

# Install LLM provider (choose one)
pip install openai        # For OpenAI
pip install anthropic     # For Anthropic
# Requesty works with OpenAI package
```

### Environment Setup

Create a `.env` file:
```env
# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password

# LLM Configuration (choose one)
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
REQUESTY_API_KEY=your_requesty_key
```

### Basic Usage

```python
from enterprise_kg_minimal import process_document

# Process a document
result = process_document(
    file_id="doc_123",
    file_content="Your document content here...",
    neo4j_uri="bolt://localhost:7687",
    neo4j_user="neo4j",
    neo4j_password="password",
    llm_provider="requesty",
    llm_model="anthropic/claude-3-5-sonnet-20241022"
)

# Check results
if result["success"]:
    print(f"✅ Created {result['chunks_created']} chunks")
    print(f"👥 Extracted {result['total_entities']} entities")
    print(f"🔗 Created {result['total_relationships']} relationships")
else:
    print(f"❌ Error: {result['error']}")
```

### Advanced Configuration

```python
result = process_document(
    file_id="doc_123",
    file_content=content,
    neo4j_uri="bolt://localhost:7687",
    neo4j_user="neo4j",
    neo4j_password="password",
    llm_provider="anthropic",
    llm_model="claude-3-5-sonnet-latest",
    chunking_strategy="hybrid",
    chunk_size=1500,
    chunk_overlap=300
)
```

## Chunking Strategies

The system supports multiple chunking strategies:

- **`fixed_size`**: Fixed character-based chunks
- **`sentence_based`**: Sentence boundary preservation
- **`paragraph_based`**: Paragraph boundary preservation
- **`semantic_based`**: Semantic coherence optimization
- **`hybrid`**: Combination approach (default)

## LLM Providers

Supported providers:
- **OpenAI**: `gpt-4o`, `gpt-3.5-turbo`
- **Anthropic**: `claude-3-5-sonnet-latest`, `claude-3-haiku`
- **Gemini**: `gemini-pro`
- **OpenRouter**: Multiple models via OpenRouter
- **Requesty**: Custom routing service

## Environment Variables

Set up your environment:

```env
# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password

# LLM Configuration (choose one)
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GEMINI_API_KEY=your_gemini_key
OPENROUTER_API_KEY=your_openrouter_key
REQUESTY_API_KEY=your_requesty_key
```

## Result Structure

The `process_document()` function returns:

```python
{
    "success": True,
    "file_id": "doc_123",
    "chunks_created": 5,
    "chunks_processed": 5,
    "chunks_failed": 0,
    "total_entities": 25,
    "total_relationships": 18,
    "file_node_created": True,
    "contains_relationships_created": 5,
    "chunk_details": [
        {
            "chunk_id": "doc_123_chunk_0_abc123",
            "chunk_index": 0,
            "entities_extracted": 6,
            "relationships_extracted": 4,
            "graph_stored": True,
            "error": None
        },
        # ... more chunks
    ]
}
```

## Graph Queries

After processing, you can query the graph:

```cypher
// Find all chunks for a file
MATCH (f:File {id: "doc_123"})-[:CONTAINS]->(c:Chunk)
RETURN c

// Find entities extracted from a specific chunk
MATCH (c:Chunk {id: "doc_123_chunk_0_abc123"})-[:EXTRACTED_FROM]->(e:Entity)
RETURN e

// Find relationships between entities in the same chunk
MATCH (c:Chunk)-[:EXTRACTED_FROM]->(e1:Entity)
MATCH (c)-[:EXTRACTED_FROM]->(e2:Entity)
MATCH (e1)-[r]->(e2)
RETURN e1, r, e2
```

## Error Handling

The system provides detailed error information:

```python
result = process_document(file_id="doc_123", file_content=content)

if not result["success"]:
    print(f"Processing failed: {result['error']}")

# Check individual chunk failures
for chunk_detail in result["chunk_details"]:
    if chunk_detail["error"]:
        print(f"Chunk {chunk_detail['chunk_id']} failed: {chunk_detail['error']}")
```

## 👥 Dev Team Integration

### Testing

Run the comprehensive test script:
```bash
cd enterprise_kg
python test_enterprise_kg_minimal.py
```

This will:
- ✅ Test document chunking with real content
- ✅ Verify entity extraction from each chunk
- ✅ Create File → Chunks → Entities graph structure
- ✅ Provide Neo4j queries to explore results

### Integration Patterns

**1. Batch Processing:**
```python
def process_documents(documents):
    results = []
    for doc_id, content in documents:
        result = process_document(doc_id, content)
        results.append(result)
    return results
```

**2. Error Handling:**
```python
result = process_document(file_id, content)
if not result["success"]:
    logger.error(f"Failed to process {file_id}: {result['error']}")
    # Handle failure appropriately
```

**3. Configuration Management:**
```python
# Use environment variables or config files
config = {
    "neo4j_uri": os.getenv("NEO4J_URI"),
    "llm_provider": "requesty",
    "llm_model": "anthropic/claude-3-5-sonnet-20241022"
}
```

### Production Considerations

- **Chunking Strategy**: Use `hybrid` for balanced performance
- **Chunk Size**: 800-1000 characters optimal for entity extraction
- **Error Recovery**: Handle individual chunk failures gracefully
- **Monitoring**: Track success rates and processing times
- **Scaling**: Consider async processing for large document volumes

## 📋 API Reference

### process_document()

```python
def process_document(
    file_id: str,                    # Unique file identifier
    file_content: str,               # Document content
    neo4j_uri: str,                  # Neo4j connection URI
    neo4j_user: str,                 # Neo4j username
    neo4j_password: str,             # Neo4j password
    neo4j_database: Optional[str] = None,  # Neo4j database
    llm_provider: str = "openai",    # LLM provider
    llm_model: str = "gpt-4o",       # LLM model
    llm_api_key: Optional[str] = None,     # LLM API key
    chunking_strategy: str = "hybrid",     # Chunking strategy
    chunk_size: int = 1000,          # Target chunk size
    chunk_overlap: int = 200         # Chunk overlap
) -> Dict[str, Any]:
```

**Returns:**
```python
{
    "success": bool,
    "file_id": str,
    "chunks_created": int,
    "chunks_processed": int,
    "chunks_failed": int,
    "total_entities": int,
    "total_relationships": int,
    "file_node_created": bool,
    "contains_relationships_created": int,
    "chunk_details": List[Dict]
}
```

## 📦 Dependencies

- `neo4j>=5.0.0`: Graph database driver
- `python-dotenv>=1.0.0`: Environment configuration
- `openai>=1.0.0`: OpenAI API client (optional)
- `anthropic>=0.7.0`: Anthropic API client (optional)

## 📄 License

This package is part of the Enterprise KG system.
