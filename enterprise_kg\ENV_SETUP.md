# Environment Setup Guide

This guide helps you configure the `.env` file for Enterprise KG standalone testing.

## Quick Setup

1. **Copy the provided `.env` file** - It's already configured with OpenRouter
2. **Set your Neo4j password** in the `.env` file
3. **Test your configuration** with the test script

## Required Configuration

### 1. Neo4j Password (Required)

Edit the `.env` file and set your Neo4j password:

```bash
NEO4J_PASSWORD="your-neo4j-password"
```

**For Neo4j Aura cloud instance:**
```bash
NEO4J_URI="bolt://your-instance.databases.neo4j.io:7687"
NEO4J_USER="neo4j"
NEO4J_PASSWORD="your-aura-password"
```

### 2. OpenRouter Configuration (Already Set)

The `.env` file is already configured with:
- OpenRouter API key
- Provider set to "openrouter"
- Model set to "anthropic/claude-3.5-sonnet"

## Testing Your Configuration

Run the configuration test script:

```bash
python test_config.py
```

This will test:
- ✅ Environment variables are loaded
- ✅ Module imports work
- ✅ LLM client initializes (OpenRouter)
- ✅ Neo4j connection works

## Running the Standalone Processor

Once configuration tests pass:

### Create Sample Documents and Process
```bash
python main.py --create-samples
```

### Process Existing Documents
```bash
python main.py --documents /path/to/your/documents
```

### Minimal Command (uses .env defaults)
```bash
python main.py
```

## Environment Variables Reference

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `OPENROUTER_API_KEY` | OpenRouter API key | - | ✅ |
| `LLM_PROVIDER` | LLM provider | openrouter | ✅ |
| `LLM_MODEL` | Model name | anthropic/claude-3.5-sonnet | ✅ |
| `NEO4J_URI` | Neo4j connection URI | bolt://localhost:7687 | ✅ |
| `NEO4J_USER` | Neo4j username | neo4j | ✅ |
| `NEO4J_PASSWORD` | Neo4j password | - | ✅ |
| `NEO4J_DATABASE` | Neo4j database name | - | ❌ |
| `DOCUMENTS_PATH` | Documents directory | documents | ❌ |
| `FILE_PATTERNS` | File extensions | .md,.txt,.pdf | ❌ |

## Available OpenRouter Models

You can change the model in `.env`:

```bash
# Anthropic models
LLM_MODEL="anthropic/claude-3.5-sonnet"
LLM_MODEL="anthropic/claude-3-haiku"

# OpenAI models  
LLM_MODEL="openai/gpt-4o"
LLM_MODEL="openai/gpt-4o-mini"

# Other providers
LLM_MODEL="google/gemini-pro-1.5"
LLM_MODEL="meta-llama/llama-3.1-8b-instruct"
```

## Troubleshooting

### Neo4j Connection Issues
- Make sure Neo4j is running
- Check URI format (bolt:// not http://)
- Verify username/password
- For Aura: use the full connection string

### OpenRouter Issues
- Verify API key is correct
- Check model name spelling
- Ensure you have credits in OpenRouter account

### Import Errors
- Install requirements: `pip install -r requirements_standalone.txt`
- Make sure you're in the enterprise_kg directory

## Command Line Overrides

You can override .env values with command line arguments:

```bash
# Use different provider
python main.py --llm-provider openai --llm-model gpt-4o

# Use different Neo4j instance
python main.py --neo4j-uri bolt://other-instance:7687 --neo4j-password other-password

# Process different directory
python main.py --documents /path/to/other/docs
```

## Next Steps

After successful configuration:

1. **Test with sample documents**: `python main.py --create-samples`
2. **Check Neo4j browser**: Open http://localhost:7474 to see extracted entities
3. **Process your documents**: `python main.py --documents /your/docs`
4. **Explore the knowledge graph**: Use Cypher queries to explore relationships

Example queries:
```cypher
// See all entities
MATCH (n:Entity) RETURN n LIMIT 10

// See all relationships
MATCH (a:Entity)-[r]->(b:Entity) RETURN a.name, type(r), b.name LIMIT 10

// Find people involved in projects
MATCH (p:Entity)-[:INVOLVED_IN]->(proj:Entity) 
WHERE p.type = 'Person' AND proj.type = 'Project'
RETURN p.name, proj.name
```
