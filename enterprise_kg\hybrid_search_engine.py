"""
Hybrid Search Engine

This module combines your existing Pinecone semantic search with the new
Enterprise Knowledge Graph for comprehensive enterprise search capabilities.
"""

import os
import sys
import json
import logging
from typing import Dict, List, Any, Optional

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from entity_discovery import HybridEntitySearch
from enterprise_kg_processor import EnterpriseKGProcessor

logger = logging.getLogger(__name__)


class HybridSearchEngine:
    """
    Hybrid search engine that combines Pinecone semantic search with Neo4j knowledge graph.
    
    This class integrates with your existing Pinecone setup and adds knowledge graph
    capabilities for comprehensive enterprise search.
    """
    
    def __init__(
        self,
        existing_pinecone_client,
        kg_processor: EnterpriseKGProcessor,
        enable_entity_discovery: bool = True
    ):
        """
        Initialize the hybrid search engine.
        
        Args:
            existing_pinecone_client: Your existing Pinecone client/index
            kg_processor: Enterprise KG processor instance
            enable_entity_discovery: Whether to enable entity discovery from Pinecone
        """
        self.pinecone_client = existing_pinecone_client
        self.kg_processor = kg_processor
        self.enable_entity_discovery = enable_entity_discovery
        
        # Initialize entity discovery if enabled
        if enable_entity_discovery:
            self.entity_search = HybridEntitySearch(
                pinecone_client=existing_pinecone_client,
                neo4j_client=kg_processor.processor.neo4j_client,
                llm_client=kg_processor.processor.llm_client
            )
        
        logger.info("Hybrid Search Engine initialized")
    
    def search(
        self,
        query: str,
        org_id: Optional[str] = None,
        method: str = "hybrid",
        top_k: int = 10
    ) -> Dict[str, Any]:
        """
        Perform hybrid search combining Pinecone and Neo4j.
        
        Args:
            query: Search query
            org_id: Organization ID for filtering (matches your Pinecone metadata)
            method: Search method ("pinecone", "neo4j", "hybrid")
            top_k: Number of top results to return
            
        Returns:
            Combined search results
        """
        logger.info(f"Searching: '{query}' (method: {method}, org_id: {org_id})")
        
        if method == "pinecone":
            return self._pinecone_only_search(query, org_id, top_k)
        elif method == "neo4j":
            return self._neo4j_only_search(query)
        else:  # hybrid
            return self._hybrid_search(query, org_id, top_k)
    
    def _pinecone_only_search(self, query: str, org_id: Optional[str], top_k: int) -> Dict[str, Any]:
        """Perform Pinecone-only search using your existing setup."""
        try:
            # Create query embedding (you'll need to adapt this to your embedding method)
            query_embedding = self._create_embedding(query)
            
            # Build filter for your existing metadata structure
            filter_metadata = {}
            if org_id:
                filter_metadata["org_id"] = org_id
            
            # Search Pinecone (adapt to your existing client interface)
            pinecone_results = self.pinecone_client.query(
                vector=query_embedding,
                top_k=top_k,
                filter=filter_metadata,
                include_metadata=True
            )
            
            return {
                "method": "pinecone_only",
                "query": query,
                "semantic_results": self._format_pinecone_results(pinecone_results),
                "source_files": self._extract_file_ids(pinecone_results),
                "total_results": len(pinecone_results.get("matches", []))
            }
            
        except Exception as e:
            logger.error(f"Pinecone search failed: {e}")
            return {"method": "pinecone_only", "error": str(e)}
    
    def _neo4j_only_search(self, query: str) -> Dict[str, Any]:
        """Perform Neo4j-only search using knowledge graph."""
        try:
            # Extract entities from query (simple approach)
            entities = self._extract_entities_from_query(query)
            
            # Query Neo4j for relationships
            relationships = []
            for entity in entities:
                entity_rels = self.kg_processor.query_relationships(
                    entity_name=entity,
                    limit=20
                )
                relationships.extend(entity_rels)
            
            return {
                "method": "neo4j_only",
                "query": query,
                "extracted_entities": entities,
                "structured_relationships": relationships,
                "total_results": len(relationships)
            }
            
        except Exception as e:
            logger.error(f"Neo4j search failed: {e}")
            return {"method": "neo4j_only", "error": str(e)}
    
    def _hybrid_search(self, query: str, org_id: Optional[str], top_k: int) -> Dict[str, Any]:
        """Perform hybrid search combining both systems."""
        try:
            # Step 1: Pinecone semantic search
            pinecone_results = self._pinecone_only_search(query, org_id, top_k)
            
            # Step 2: Entity discovery and Neo4j search
            if self.enable_entity_discovery:
                kg_results = self.entity_search.search(query, org_id)
            else:
                kg_results = self._neo4j_only_search(query)
            
            # Step 3: Combine and enhance results
            combined_results = self._combine_results(pinecone_results, kg_results, query)
            
            return combined_results
            
        except Exception as e:
            logger.error(f"Hybrid search failed: {e}")
            return {"method": "hybrid", "error": str(e)}
    
    def _combine_results(
        self,
        pinecone_results: Dict[str, Any],
        kg_results: Dict[str, Any],
        query: str
    ) -> Dict[str, Any]:
        """Combine results from both search systems."""
        
        # Extract semantic context from Pinecone
        semantic_chunks = pinecone_results.get("semantic_results", [])
        
        # Extract structured relationships from Neo4j
        structured_relationships = kg_results.get("structured_relationships", [])
        
        # Get source files from both systems
        pinecone_files = pinecone_results.get("source_files", [])
        kg_files = kg_results.get("source_files", [])
        all_source_files = list(set(pinecone_files + kg_files))
        
        # Generate comprehensive answer
        answer = self._generate_comprehensive_answer(
            query, semantic_chunks, structured_relationships
        )
        
        return {
            "method": "hybrid",
            "query": query,
            "answer": answer,
            "semantic_search": {
                "chunks": semantic_chunks,
                "total_chunks": len(semantic_chunks)
            },
            "knowledge_graph": {
                "relationships": structured_relationships,
                "total_relationships": len(structured_relationships),
                "discovered_entities": kg_results.get("discovered_entities", [])
            },
            "source_files": all_source_files,
            "confidence": self._calculate_confidence(pinecone_results, kg_results),
            "processing_info": {
                "pinecone_results": len(semantic_chunks),
                "neo4j_results": len(structured_relationships),
                "combined_sources": len(all_source_files)
            }
        }
    
    def _generate_comprehensive_answer(
        self,
        query: str,
        semantic_chunks: List[str],
        structured_relationships: List[Dict]
    ) -> str:
        """Generate a comprehensive answer using LLM."""
        try:
            # Prepare context
            context_parts = []
            
            if semantic_chunks:
                context_parts.append("Document Context:")
                context_parts.extend([f"- {chunk[:200]}..." for chunk in semantic_chunks[:3]])
            
            if structured_relationships:
                context_parts.append("\nStructured Knowledge:")
                for rel in structured_relationships[:5]:
                    if isinstance(rel, dict):
                        # Format relationship info
                        source = rel.get("source", {}).get("name", "Unknown")
                        relationship = rel.get("relationship", {})
                        target = rel.get("target", {}).get("name", "Unknown")
                        rel_type = relationship.get("type", "RELATED_TO") if isinstance(relationship, dict) else str(relationship)
                        context_parts.append(f"- {source} {rel_type} {target}")
            
            context = "\n".join(context_parts)
            
            # Generate answer using LLM
            prompt = f"""
Based on the following enterprise data, provide a comprehensive answer to: {query}

{context}

Please provide a clear, concise answer that combines both the document context and structured relationships.
"""
            
            response = self.kg_processor.processor.llm_client.generate_structured_response(
                prompt, '"Your comprehensive answer here"'
            )
            
            return response if isinstance(response, str) else "Unable to generate comprehensive answer."
            
        except Exception as e:
            logger.error(f"Answer generation failed: {e}")
            return f"Found {len(semantic_chunks)} relevant documents and {len(structured_relationships)} relationships."
    
    def _create_embedding(self, text: str) -> List[float]:
        """
        Create embedding for text.
        
        NOTE: You need to replace this with your existing embedding method.
        """
        # TODO: Replace with your existing embedding creation logic
        # This is a placeholder - you should use your existing method
        logger.warning("Using placeholder embedding - replace with your existing method")
        return [0.1] * 1536  # Placeholder embedding
    
    def _extract_entities_from_query(self, query: str) -> List[str]:
        """Simple entity extraction from query."""
        import re
        # Extract capitalized words/phrases
        entities = re.findall(r'\b[A-Z][a-zA-Z\s]*(?:[A-Z][a-zA-Z]*)*\b', query)
        # Filter common words
        common_words = {"What", "Who", "How", "Where", "When", "Which", "The", "And", "Or"}
        return [entity.strip() for entity in entities if entity.strip() not in common_words]
    
    def _format_pinecone_results(self, pinecone_results: Dict) -> List[str]:
        """Format Pinecone results to extract chunk texts."""
        chunks = []
        for match in pinecone_results.get("matches", []):
            chunk_text = match.get("metadata", {}).get("chunk_text", "")
            if chunk_text:
                chunks.append(chunk_text)
        return chunks
    
    def _extract_file_ids(self, pinecone_results: Dict) -> List[str]:
        """Extract file IDs from Pinecone results."""
        file_ids = []
        for match in pinecone_results.get("matches", []):
            file_id = match.get("metadata", {}).get("file_id", "")
            if file_id:
                file_ids.append(file_id)
        return list(set(file_ids))
    
    def _calculate_confidence(self, pinecone_results: Dict, kg_results: Dict) -> float:
        """Calculate confidence score for combined results."""
        pinecone_score = 0.0
        kg_score = 0.0
        
        # Pinecone confidence (based on similarity scores)
        matches = pinecone_results.get("semantic_results", [])
        if matches:
            pinecone_score = 0.8  # Placeholder - use actual similarity scores
        
        # KG confidence (based on number of relationships found)
        relationships = kg_results.get("structured_relationships", [])
        if relationships:
            kg_score = min(len(relationships) / 10.0, 1.0)  # Normalize to 0-1
        
        # Combined confidence
        return (pinecone_score + kg_score) / 2.0
    
    def get_search_stats(self) -> Dict[str, Any]:
        """Get statistics about the search system."""
        kg_stats = self.kg_processor.get_processing_stats()
        
        return {
            "knowledge_graph": kg_stats,
            "search_engine": {
                "entity_discovery_enabled": self.enable_entity_discovery,
                "pinecone_connected": self.pinecone_client is not None,
                "timestamp": kg_stats.get("timestamp")
            }
        }


# Factory function for easy creation
def create_hybrid_search_engine(
    existing_pinecone_client,
    neo4j_config: Dict[str, str],
    llm_config: Dict[str, str]
) -> HybridSearchEngine:
    """
    Factory function to create a hybrid search engine.
    
    Args:
        existing_pinecone_client: Your existing Pinecone client
        neo4j_config: Neo4j configuration
        llm_config: LLM configuration
        
    Returns:
        Configured HybridSearchEngine instance
    """
    # Create KG processor
    kg_processor = EnterpriseKGProcessor(neo4j_config, llm_config)
    
    # Create hybrid search engine
    return HybridSearchEngine(existing_pinecone_client, kg_processor)


# Example usage
if __name__ == "__main__":
    # Example configuration
    neo4j_config = {
        "uri": os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        "user": os.getenv("NEO4J_USER", "neo4j"),
        "password": os.getenv("NEO4J_PASSWORD", "password")
    }
    
    llm_config = {
        "provider": "openai",
        "model": "gpt-4o"
    }
    
    # Mock Pinecone client for testing
    class MockPineconeClient:
        def query(self, vector, top_k, filter=None, include_metadata=True):
            return {
                "matches": [
                    {
                        "id": "doc1_chunk1",
                        "score": 0.9,
                        "metadata": {
                            "file_id": "test_document.md",
                            "org_id": "test_org",
                            "chunk_text": "This is a test document chunk."
                        }
                    }
                ]
            }
    
    # Create hybrid search engine
    mock_pinecone = MockPineconeClient()
    search_engine = create_hybrid_search_engine(mock_pinecone, neo4j_config, llm_config)
    
    # Test search
    results = search_engine.search("Who is working on Project Alpha?", org_id="test_org")
    print(json.dumps(results, indent=2))
